{"account_tracker_service_last_update": "********120614774", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 121}, "autofill": {"last_version_deduped": 121}, "browser": {"window_placement": {"bottom": 300, "left": 1440, "maximized": false, "right": 1956, "top": 0, "work_area_bottom": 1392, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 0}}, "countryid_at_install": 22094, "default_apps_install_state": 2, "dips_timer_last_update": "********120534152", "domain_diversity": {"last_reporting_timestamp": "********120614181"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "121.0.6167.85"}, "gaia_cookie": {"changed_time": **********.843391, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.googlechromefortesting.windows"}, "google": {"services": {"signin_scoped_device_id": "1c8785e6-405e-4bb1-a2c6-024cb96e7100"}}, "in_product_help": {"session_last_active_time": "*****************", "session_start_time": "********120489184"}, "intl": {"accept_languages": "en-US,en", "selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "ntp": {"num_personal_suggestions": 2}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "access_to_get_all_screens_media_in_session": {}, "accessibility_events": {}, "anti_abuse": {}, "app_banner": {"https://www.facebook.com:443,*": {"last_modified": "********125933201", "setting": {"https://www.facebook.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "********125932337"}}, "https://www.facebook.com/?ref=homescreenpwa": {"couldShowBannerEvents": 1.3396494125933178e+16}}}}, "ar": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {"https://www.facebook.com:443,*": {"last_modified": "********188039473", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "cookie_controls_metadata": {"https://[*.]facebook.com,*": {"last_modified": "********188061676", "setting": {}}}, "cookies": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "********120844315", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {"https://www.facebook.com:443,*": {"last_modified": "********136801472", "setting": {"UserDataFieldFilled": true}}}, "geolocation": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"https://www.facebook.com:443,*": {"last_modified": "********188062645", "setting": {"Notifications": {"ignore_count": 1}}}}, "permission_autorevocation_data": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://www.facebook.com:443,*": {"last_modified": "********167942940", "setting": {"lastEngagementTime": 1.3396494167942904e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_storage_access": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"notifications": [{"action": 3, "prompt_disposition": 1, "time": "********188062596"}]}, "pref_version": 1}, "creation_time": "*****************", "exit_type": "Crashed", "last_engagement_time": "********167942903", "last_time_obsolete_http_credentials_removed": **********.53852, "last_time_password_store_metrics_reported": **********.535862, "managed": {"banner_state": 2}, "managed_user_id": "", "name": "Person 1", "password_account_storage_settings": {}, "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "********120"}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EOLK/s2lgeYXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEJzM/s2lgeYXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ08j+zaWB5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAcYBCACEMjL/s2lgeYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "*****************"}, "sessions": {"event_log": [{"crashed": false, "time": "********120479616", "type": 0}], "session_data_status": 1}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": true}, "supervised_user": {"metrics": {"day_id": 155051}}, "sync": {"autofill_wallet_import_enabled_migrated": true}, "tracking_protection": {"tracking_protection_3pcd_enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://www.facebook.com/?ref=homescreenpwa": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "13396467600000000", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "error_loaded_policy_apps_migrated": true, "last_preinstall_synchronize_version": "121"}}