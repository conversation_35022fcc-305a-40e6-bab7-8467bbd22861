// FB NEXUS - Frontend JavaScript
class FBNexusApp {
    constructor() {
        this._accounts = [];
        this.tasks = [];
        this.proxies = [];
        this.logs = [];

        // Debug accounts array changes
        Object.defineProperty(this, 'accounts', {
            get: () => this._accounts,
            set: (value) => {
                console.log('🔄 Accounts array changed:', value.length, 'accounts');
                console.trace('Stack trace:');
                this._accounts = value;
            }
        });
        this.settings = {
            delayBetweenActions: 3000,
            maxActionsSession: 50,
            sessionBreak: 15,
            randomizeDelays: true,
            headlessMode: false,
            disableImages: true,
            maxBrowsers: 5,
            browserTimeout: 30,
            autoProxyRotation: true,
            fingerprintSpoofing: true,
            clearCookies: false,
            userAgentRotation: 'random',
            commentTemplates: [
                'Great post! 👍',
                'Thanks for sharing!',
                'Interesting! 🤔',
                'Love this! ❤️',
                'Amazing content!',
                'Nice work! 🔥'
            ],
            randomComments: true,
            desktopNotifications: true,
            soundNotifications: false,
            emailNotifications: false,
            notificationEmail: '',
            logLevel: 'info',
            autoBackup: true,
            autoUpdateCheck: true,
            apiRateLimit: 60
        };
        this.stats = {
            totalAccounts: 0,
            activeAccounts: 0,
            runningTasks: 0,
            successRate: 0
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadData();
        this.startStatsUpdater();

        // Restore last active tab or default to automation
        const lastActiveTab = localStorage.getItem('fb-nexus-active-tab') || 'automation';
        this.showPage(lastActiveTab);

        // Update nav to match restored tab
        const navItem = document.querySelector(`[data-page="${lastActiveTab}"]`);
        if (navItem) {
            this.updateActiveNav(navItem);
        }
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.showPage(page);
                this.updateActiveNav(item);
            });
        });

        // Automation buttons
        document.querySelectorAll('.start-automation').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                this.startAutomation(action, btn);
            });
        });

        // Modal controls
        document.getElementById('add-account-btn').addEventListener('click', () => {
            this.showModal('add-account-modal');
        });

        document.getElementById('bulk-add-accounts').addEventListener('click', () => {
            this.showBulkAddModal();
        });

        document.getElementById('save-account').addEventListener('click', () => {
            this.saveAccount();
        });

        document.getElementById('cancel-add-account').addEventListener('click', () => {
            this.hideModal('add-account-modal');
        });

        // Close modal when clicking X or outside
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.hideModal(modal.id);
            });
        });

        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideModal(e.target.id);
            }
        });

        // Task controls
        document.getElementById('pause-all-btn').addEventListener('click', () => {
            this.pauseAllTasks();
        });

        document.getElementById('resume-all-btn').addEventListener('click', () => {
            this.resumeAllTasks();
        });

        document.getElementById('check-all-accounts').addEventListener('click', () => {
            this.checkAllAccounts();
        });

        document.getElementById('clear-all-accounts').addEventListener('click', () => {
            this.clearAllAccounts();
        });

        document.getElementById('login-all-accounts').addEventListener('click', () => {
            this.loginAllAccounts();
        });

        document.getElementById('arrange-browsers').addEventListener('click', () => {
            this.arrangeBrowsers();
        });

        document.getElementById('auto-login-all').addEventListener('click', () => {
            this.startAutoLogin();
        });

        document.getElementById('auto-login-stop').addEventListener('click', () => {
            this.stopAutoLogin();
        });

        // Proxy controls
        document.getElementById('bulk-add-proxies').addEventListener('click', () => {
            this.showBulkProxiesModal();
        });

        document.getElementById('test-all-proxies').addEventListener('click', () => {
            this.testAllProxies();
        });

        document.getElementById('clear-all-proxies').addEventListener('click', () => {
            this.clearAllProxies();
        });

        // Settings controls
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });

        // Logs controls
        document.getElementById('refresh-logs').addEventListener('click', () => {
            this.refreshLogs();
        });

        document.getElementById('clear-logs').addEventListener('click', () => {
            this.clearLogs();
        });

        document.getElementById('export-logs').addEventListener('click', () => {
            this.exportLogs();
        });

        document.getElementById('log-level-filter').addEventListener('change', (e) => {
            this.filterLogs(e.target.value);
        });
    }

    showPage(pageId) {
        // Hide all pages
        document.querySelectorAll('.page-content').forEach(page => {
            page.classList.remove('active');
        });

        // Show selected page
        document.getElementById(`${pageId}-page`).classList.add('active');

        // Save current tab to localStorage
        localStorage.setItem('fb-nexus-active-tab', pageId);

        // Update page title
        const titles = {
            dashboard: 'Dashboard',
            accounts: 'Accounts',
            proxies: 'Proxies',
            automation: 'Automation',
            logs: 'Logs',
            settings: 'Settings'
        };
        document.getElementById('page-title').textContent = titles[pageId] || 'FB NEXUS';
    }

    updateActiveNav(activeItem) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    async startAutomation(action, button) {
        if (this.accounts.length === 0) {
            this.showNotification('Please add Facebook accounts first!', 'warning');
            return;
        }

        const card = button.closest('.automation-card');
        const statusSpan = card.querySelector('.card-stats span:first-child span');
        const countSpan = card.querySelector(`#${action}-count`);

        // Update UI
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';
        button.disabled = true;
        statusSpan.textContent = 'Starting...';
        statusSpan.className = 'status-active';

        try {
            // Call backend API
            const response = await fetch('/api/automation/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    accounts: this.accounts.map(acc => acc.id)
                })
            });

            const result = await response.json();

            if (result.success) {
                // Update UI for success
                button.innerHTML = '<i class="fas fa-stop"></i> Stop ' + this.capitalizeFirst(action);
                button.classList.remove('btn-primary');
                button.classList.add('btn-danger');
                statusSpan.textContent = 'Running';
                
                // Add task to table
                this.addTaskToTable({
                    id: result.taskId,
                    type: action,
                    account: 'Multiple Accounts',
                    target: result.target || 'Auto-detected',
                    status: 'running',
                    progress: 0
                });

                this.showNotification(`${this.capitalizeFirst(action)} automation started successfully!`, 'success');
                
                // Change button to stop functionality
                button.onclick = () => this.stopAutomation(action, button, result.taskId);
                
            } else {
                throw new Error(result.message || 'Failed to start automation');
            }

        } catch (error) {
            console.error('Automation start error:', error);
            this.showNotification('Failed to start automation: ' + error.message, 'error');
            
            // Reset button
            button.innerHTML = '<i class="fas fa-play"></i> Start Auto ' + this.capitalizeFirst(action);
            button.disabled = false;
            statusSpan.textContent = 'Inactive';
            statusSpan.className = 'status-inactive';
        }
    }

    async stopAutomation(action, button, taskId) {
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Stopping...';
        button.disabled = true;

        try {
            const response = await fetch('/api/automation/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ taskId })
            });

            const result = await response.json();

            if (result.success) {
                // Reset button
                button.innerHTML = '<i class="fas fa-play"></i> Start Auto ' + this.capitalizeFirst(action);
                button.classList.remove('btn-danger');
                button.classList.add('btn-primary');
                button.disabled = false;

                // Update status
                const card = button.closest('.automation-card');
                const statusSpan = card.querySelector('.card-stats span:first-child span');
                statusSpan.textContent = 'Inactive';
                statusSpan.className = 'status-inactive';

                // Remove task from table
                this.removeTaskFromTable(taskId);

                this.showNotification(`${this.capitalizeFirst(action)} automation stopped.`, 'info');
                
                // Restore original click handler
                button.onclick = () => this.startAutomation(action, button);

            } else {
                throw new Error(result.message || 'Failed to stop automation');
            }

        } catch (error) {
            console.error('Automation stop error:', error);
            this.showNotification('Failed to stop automation: ' + error.message, 'error');
            button.disabled = false;
        }
    }

    addTaskToTable(task) {
        const tbody = document.getElementById('tasks-tbody');
        const row = document.createElement('tr');
        row.dataset.taskId = task.id;
        
        row.innerHTML = `
            <td><span class="status-badge status-${task.type}">${task.type.toUpperCase()}</span></td>
            <td>${task.account}</td>
            <td>${task.target}</td>
            <td><span class="status-badge status-${task.status}">${task.status.toUpperCase()}</span></td>
            <td>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${task.progress}%"></div>
                </div>
                <small>${task.progress}%</small>
            </td>
            <td>
                <button class="btn btn-secondary btn-sm" onclick="app.pauseTask('${task.id}')">
                    <i class="fas fa-pause"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="app.stopTask('${task.id}')">
                    <i class="fas fa-stop"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
        this.tasks.push(task);
        this.updateStats();
    }

    removeTaskFromTable(taskId) {
        const row = document.querySelector(`tr[data-task-id="${taskId}"]`);
        if (row) {
            row.remove();
        }
        this.tasks = this.tasks.filter(task => task.id !== taskId);
        this.updateStats();
    }

    async saveAccount() {
        console.log('🔧 saveAccount called');
        const email = document.getElementById('account-email').value;
        const password = document.getElementById('account-password').value;
        const twoFA = document.getElementById('account-2fa').value;
        let proxyId = document.getElementById('account-proxy-select').value;

        console.log('📝 Form data:', { email, password: '***', twoFA, proxyId });

        if (!email || !password) {
            this.showNotification('Email và mật khẩu là bắt buộc!', 'warning');
            return;
        }

        try {
            // Handle auto proxy assignment
            if (proxyId === 'auto') {
                proxyId = await this.getNextAvailableProxy();
                if (!proxyId) {
                    this.showNotification('❌ Không còn proxy nào khả dụng! Vui lòng thêm proxy hoặc chọn "Không Proxy"', 'error');
                    return;
                }
                console.log('🤖 Auto-assigned proxy:', proxyId);
            }

            const response = await fetch('/api/accounts/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email,
                    password,
                    twoFA,
                    proxyId: proxyId || undefined
                })
            });

            const result = await response.json();

            if (result.success) {
                this.accounts.push(result.account);
                this.renderAccounts();
                this.updateStats();
                this.hideModal('add-account-modal');
                this.clearForm('add-account-form');

                if (proxyId) {
                    const proxy = this.proxies.find(p => p.id === proxyId);
                    const proxyInfo = proxy ? `${proxy.ip}:${proxy.port}` : proxyId;
                    this.showNotification(`✅ Đã thêm tài khoản với proxy: ${proxyInfo}`, 'success');
                } else {
                    this.showNotification('✅ Đã thêm tài khoản thành công!', 'success');
                }
            } else {
                throw new Error(result.message || 'Failed to add account');
            }

        } catch (error) {
            console.error('Add account error:', error);
            this.showNotification('Lỗi khi thêm tài khoản: ' + error.message, 'error');
        }
    }

    async getNextAvailableProxy() {
        try {
            // Get all proxies
            const response = await fetch('/api/proxies');
            const data = await response.json();

            if (!data.success || !data.proxies || data.proxies.length === 0) {
                return null;
            }

            // Get available proxies (not assigned to any account)
            const availableProxies = await this.getAvailableProxies(data.proxies);

            if (availableProxies.length === 0) {
                return null;
            }

            // Return first available proxy
            return availableProxies[0].id;

        } catch (error) {
            console.error('Error getting available proxy:', error);
            return null;
        }
    }

    async clearAllAccounts() {
        if (!confirm('⚠️ BẠN CÓ CHẮC CHẮN MUỐN XÓA TẤT CẢ TÀI KHOẢN?\n\nHành động này KHÔNG THỂ HOÀN TÁC!')) {
            return;
        }

        if (!confirm('🚨 XÁC NHẬN LẦN CUỐI!\n\nTất cả tài khoản sẽ bị xóa vĩnh viễn!')) {
            return;
        }

        try {
            this.showNotification('🗑️ Đang xóa tất cả tài khoản...', 'info');

            const response = await fetch('/api/accounts/clear-all', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                // Clear frontend data
                this.accounts = [];
                this.renderAccounts();
                this.updateStats();
                this.showNotification('✅ Đã xóa tất cả tài khoản thành công!', 'success');
            } else {
                throw new Error(result.message || 'Failed to clear accounts');
            }

        } catch (error) {
            console.error('Clear all accounts error:', error);
            this.showNotification('❌ Lỗi khi xóa tài khoản: ' + error.message, 'error');
        }
    }

    async loginAllAccounts() {
        console.log('🔍 Debug - Total accounts:', this.accounts.length);
        console.log('🔍 Debug - Accounts data:', this.accounts);

        // Filter accounts that have email (password is stored encrypted in database)
        const activeAccounts = this.accounts.filter(account => account.email && account.email.trim() !== '');

        console.log('🔍 Debug - Filtered active accounts:', activeAccounts.length);

        if (activeAccounts.length === 0) {
            this.showNotification('❌ Không có tài khoản nào để login! Hãy thêm accounts trước.', 'warning');
            return;
        }

        console.log('🚀 Found accounts for bulk login:', activeAccounts.length);
        console.log('📋 Accounts:', activeAccounts.map(acc => ({ id: acc.id, email: acc.email })));

        // Giới hạn số lượng accounts để tránh overload và browsers bị nhỏ
        let maxAccounts = 6; // Default 6 accounts cho display tốt hơn

        if (activeAccounts.length > maxAccounts) {
            const userChoice = prompt(`⚠️ BẠN CÓ ${activeAccounts.length} ACCOUNTS!\n\n🖥️ ĐỂ BROWSERS HIỂN THỊ RÕ RÀNG:\n• Tối đa 6 accounts: Window size lớn\n• 7-12 accounts: Window size trung bình\n• >12 accounts: Window size nhỏ\n\nNhập số lượng accounts muốn login (1-${Math.min(activeAccounts.length, 12)}):`, maxAccounts);

            if (userChoice === null) return; // User cancelled

            const chosenCount = parseInt(userChoice);
            if (isNaN(chosenCount) || chosenCount < 1 || chosenCount > 12) {
                this.showNotification('❌ Số lượng không hợp lệ! Phải từ 1-12 accounts để display tốt.', 'error');
                return;
            }

            maxAccounts = chosenCount;
            activeAccounts.splice(maxAccounts); // Chỉ lấy số lượng user chọn
        }

        const estimatedTime = Math.ceil(activeAccounts.length / 2) * 10; // Ước tính thời gian

        if (!confirm(`🚀 BẮT ĐẦU LOGIN ${activeAccounts.length} TÀI KHOẢN?\n\n📋 STRATEGY:\n• VN IP First (tránh captcha)\n• Login 2 accounts/batch\n• Delay 5s giữa các batch\n• Auto arrange browsers\n\n⏱️ Thời gian ước tính: ~${estimatedTime} phút\n\nTiếp tục?`)) {
            return;
        }

        try {
            this.showNotification(`🚀 Bắt đầu login ${activeAccounts.length} tài khoản...`, 'info');

            // Update button state
            const loginBtn = document.getElementById('login-all-accounts');
            const originalText = loginBtn.innerHTML;
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang Login...';

            // Launch browsers first (parallel)
            this.showNotification('🌐 Đang khởi động browsers...', 'info');
            const launchPromises = activeAccounts.map(account => this.launchBrowser(account.id));
            await Promise.allSettled(launchPromises);

            // Wait a bit for browsers to fully load
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Arrange browsers
            await this.arrangeBrowsers();

            // Login accounts (parallel with limit)
            this.showNotification('🔐 Bắt đầu login process...', 'info');
            const batchSize = 2; // Login chỉ 2 accounts cùng lúc để tránh detection
            const batches = [];

            for (let i = 0; i < activeAccounts.length; i += batchSize) {
                batches.push(activeAccounts.slice(i, i + batchSize));
            }

            let successCount = 0;
            let failCount = 0;

            for (const batch of batches) {
                const loginPromises = batch.map(async (account) => {
                    try {
                        // Update UI to show login in progress
                        this.updateAccountStatus(account.id, 'logging_in', '🔄 Đang login...');

                        const result = await this.loginFacebookAccount(account.id);
                        if (result.success) {
                            successCount++;
                            this.showNotification(`✅ ${account.email} login thành công!`, 'success');
                            // Update UI to show success
                            this.updateAccountStatus(account.id, 'logged_in', '✅ Đã login');
                        } else {
                            failCount++;
                            this.showNotification(`❌ ${account.email} login thất bại!`, 'error');
                            // Update UI to show failure
                            this.updateAccountStatus(account.id, 'login_failed', '❌ Login thất bại');
                        }
                        return result;
                    } catch (error) {
                        failCount++;
                        this.showNotification(`❌ ${account.email} login lỗi: ${error.message}`, 'error');
                        // Update UI to show error
                        this.updateAccountStatus(account.id, 'login_failed', '❌ Lỗi login');
                        return { success: false, error: error.message };
                    }
                });

                await Promise.allSettled(loginPromises);

                // Wait between batches để tránh spam
                if (batches.indexOf(batch) < batches.length - 1) {
                    this.showNotification(`⏳ Chờ 5 giây trước batch tiếp theo...`, 'info');
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Tăng delay lên 5 giây
                }
            }

            // Final arrangement
            await this.arrangeBrowsers();

            // Show final results
            this.showNotification(
                `🎉 Hoàn thành! ✅ ${successCount} thành công, ❌ ${failCount} thất bại`,
                successCount > failCount ? 'success' : 'warning'
            );

            // Restore button
            loginBtn.disabled = false;
            loginBtn.innerHTML = originalText;

            // Refresh accounts data to get latest status
            await this.loadAccounts();

            // Update progress display
            this.updateBulkLoginProgress(successCount, failCount, activeAccounts.length);

        } catch (error) {
            console.error('Login all accounts error:', error);
            this.showNotification('❌ Lỗi khi login tất cả: ' + error.message, 'error');

            // Restore button
            const loginBtn = document.getElementById('login-all-accounts');
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-rocket"></i> Login Tất Cả';
        }
    }

    async launchBrowser(accountId) {
        try {
            const response = await fetch(`/api/accounts/${accountId}/launch-browser`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            return await response.json();
        } catch (error) {
            console.error(`Launch browser error for ${accountId}:`, error);
            return { success: false, error: error.message };
        }
    }

    async loginFacebookAccount(accountId) {
        try {
            const response = await fetch(`/api/accounts/${accountId}/login-facebook`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const result = await response.json();
            return result;
        } catch (error) {
            console.error(`Login error for ${accountId}:`, error);
            return { success: false, error: error.message };
        }
    }

    async arrangeBrowsers() {
        try {
            this.showNotification('📐 Đang sắp xếp browsers...', 'info');

            const response = await fetch('/api/browsers/arrange', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                this.showNotification('✅ Đã sắp xếp browsers thành công!', 'success');
            } else {
                throw new Error(result.message || 'Failed to arrange browsers');
            }

        } catch (error) {
            console.error('Arrange browsers error:', error);
            this.showNotification('❌ Lỗi khi sắp xếp browsers: ' + error.message, 'error');
        }
    }

    updateAccountStatus(accountId, status, statusText) {
        try {
            // Find account in array and update
            const accountIndex = this.accounts.findIndex(acc => acc.id === accountId);
            if (accountIndex !== -1) {
                this.accounts[accountIndex].status = status;
                this.accounts[accountIndex].isActive = status === 'logged_in';

                // Update UI immediately
                const accountRow = document.querySelector(`[data-account-id="${accountId}"]`);
                if (accountRow) {
                    const statusCell = accountRow.querySelector('.account-status');
                    const actionCell = accountRow.querySelector('.account-actions');

                    if (statusCell) {
                        // Update status display
                        statusCell.innerHTML = `<span class="status-${status}">${statusText}</span>`;

                        // Add animation
                        statusCell.style.animation = 'pulse 0.5s ease-in-out';
                        setTimeout(() => {
                            statusCell.style.animation = '';
                        }, 500);
                    }

                    if (actionCell) {
                        // Update action buttons based on status
                        this.updateAccountActionButtons(accountId, status, actionCell);
                    }
                }
            }

            // Update stats
            this.updateStats();

        } catch (error) {
            console.error('Update account status error:', error);
        }
    }

    updateAccountActionButtons(accountId, status, actionCell) {
        let buttonsHTML = '';

        switch (status) {
            case 'logging_in':
                buttonsHTML = `
                    <button class="btn btn-sm btn-secondary" disabled>
                        <i class="fas fa-spinner fa-spin"></i> Đang login...
                    </button>
                `;
                break;

            case 'logged_in':
                buttonsHTML = `
                    <button class="btn btn-sm btn-success" onclick="app.launchBrowser('${accountId}')">
                        <i class="fas fa-rocket"></i> Mở Browser
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="app.logoutAccount('${accountId}')">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteAccount('${accountId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                break;

            case 'login_failed':
                buttonsHTML = `
                    <button class="btn btn-sm btn-primary" onclick="app.loginAccount('${accountId}')">
                        <i class="fas fa-sign-in-alt"></i> Thử lại
                    </button>
                    <button class="btn btn-sm btn-info" onclick="app.checkAccount('${accountId}')">
                        <i class="fas fa-check"></i> Kiểm tra
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteAccount('${accountId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                break;

            default:
                buttonsHTML = `
                    <button class="btn btn-sm btn-primary" onclick="app.loginAccount('${accountId}')">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                    <button class="btn btn-sm btn-info" onclick="app.checkAccount('${accountId}')">
                        <i class="fas fa-check"></i> Kiểm tra
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteAccount('${accountId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
        }

        actionCell.innerHTML = buttonsHTML;
    }

    updateBulkLoginProgress(successCount, failCount, totalCount) {
        try {
            // Update stats display
            this.updateStats();

            // Show progress in notification area or create progress display
            const progressText = `📊 Tiến độ Login: ✅ ${successCount} thành công, ❌ ${failCount} thất bại, 📊 ${totalCount} tổng cộng`;

            // Update page title with progress
            document.title = `FB NEXUS - ${successCount}/${totalCount} đã login`;

            // Create or update progress bar if needed
            this.updateProgressBar(successCount + failCount, totalCount);

        } catch (error) {
            console.error('Update bulk login progress error:', error);
        }
    }

    updateProgressBar(completed, total) {
        try {
            let progressContainer = document.getElementById('bulk-login-progress');

            if (!progressContainer && completed < total) {
                // Create progress bar
                progressContainer = document.createElement('div');
                progressContainer.id = 'bulk-login-progress';
                progressContainer.className = 'bulk-progress-container';
                progressContainer.innerHTML = `
                    <div class="progress-header">
                        <span>🚀 Đang thực hiện Login hàng loạt...</span>
                        <span class="progress-text">${completed}/${total}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${(completed/total)*100}%"></div>
                    </div>
                `;

                // Insert at top of accounts section
                const accountsSection = document.querySelector('.accounts-section');
                if (accountsSection) {
                    accountsSection.insertBefore(progressContainer, accountsSection.firstChild);
                }
            } else if (progressContainer) {
                // Update existing progress bar
                const progressText = progressContainer.querySelector('.progress-text');
                const progressFill = progressContainer.querySelector('.progress-fill');

                if (progressText) {
                    progressText.textContent = `${completed}/${total}`;
                }

                if (progressFill) {
                    progressFill.style.width = `${(completed/total)*100}%`;
                }

                // Remove progress bar when complete
                if (completed >= total) {
                    setTimeout(() => {
                        if (progressContainer.parentNode) {
                            progressContainer.parentNode.removeChild(progressContainer);
                        }
                        // Reset page title
                        document.title = 'FB NEXUS - Facebook Account Manager';
                    }, 3000);
                }
            }

        } catch (error) {
            console.error('Update progress bar error:', error);
        }
    }

    async startAutoLogin() {
        try {
            const confirmMsg = `🤖 BẮT ĐẦU AUTO LOGIN HOÀN TOÀN TỰ ĐỘNG?\n\n✅ Tự động login từng nick một\n✅ Tránh captcha bằng delay\n✅ Tự động gắn proxy US\n✅ Hiển thị thông tin chi tiết\n\n⚠️ Captcha sẽ được skip và retry sau`;

            if (!confirm(confirmMsg)) return;

            this.showNotification('🚀 Bắt đầu Auto Login System...', 'info');

            // Update UI
            document.getElementById('auto-login-all').style.display = 'none';
            document.getElementById('auto-login-stop').style.display = 'inline-flex';

            const response = await fetch('/api/auto-login/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('✅ Auto Login đã bắt đầu!', 'success');
                this.startAutoLoginStatusCheck();
            } else {
                this.showNotification('❌ Lỗi: ' + result.message, 'error');
                this.resetAutoLoginUI();
            }

        } catch (error) {
            console.error('Auto login error:', error);
            this.showNotification('❌ Lỗi khi bắt đầu auto login: ' + error.message, 'error');
            this.resetAutoLoginUI();
        }
    }

    async stopAutoLogin() {
        try {
            const response = await fetch('/api/auto-login/stop', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('🛑 Auto Login đã dừng!', 'warning');
            }

            this.resetAutoLoginUI();

        } catch (error) {
            console.error('Stop auto login error:', error);
            this.showNotification('❌ Lỗi khi dừng auto login: ' + error.message, 'error');
        }
    }

    startAutoLoginStatusCheck() {
        this.autoLoginInterval = setInterval(async () => {
            try {
                const response = await fetch('/api/auto-login/status');
                const result = await response.json();

                if (result.success) {
                    const status = result.data;

                    // Update page title with progress
                    document.title = `FB NEXUS - Auto Login: ✅${status.successCount} ❌${status.failCount} 🤖${status.captchaCount}`;

                    // Show current status
                    if (status.currentAccount) {
                        this.showNotification(`🔄 Đang login: ${status.currentAccount} (${status.queueLength} còn lại)`, 'info');
                    }

                    // Check if completed
                    if (!status.isRunning && status.queueLength === 0) {
                        this.showNotification(`🎉 Auto Login hoàn thành! ✅${status.successCount} ❌${status.failCount} 🤖${status.captchaCount}`, 'success');
                        this.resetAutoLoginUI();
                        await this.loadAccounts(); // Refresh data
                    }
                }

            } catch (error) {
                console.error('Status check error:', error);
            }
        }, 5000); // Check every 5 seconds
    }

    resetAutoLoginUI() {
        document.getElementById('auto-login-all').style.display = 'inline-flex';
        document.getElementById('auto-login-stop').style.display = 'none';
        document.title = 'FB NEXUS - Facebook Account Manager';

        if (this.autoLoginInterval) {
            clearInterval(this.autoLoginInterval);
            this.autoLoginInterval = null;
        }
    }

    async clearAllProxies() {
        if (!confirm('⚠️ BẠN CÓ CHẮC CHẮN MUỐN XÓA TẤT CẢ PROXY?\n\nHành động này KHÔNG THỂ HOÀN TÁC!')) {
            return;
        }

        if (!confirm('🚨 XÁC NHẬN LẦN CUỐI!\n\nTất cả proxy sẽ bị xóa vĩnh viễn!')) {
            return;
        }

        try {
            this.showNotification('🗑️ Đang xóa tất cả proxy...', 'info');

            const response = await fetch('/api/proxies/clear-all', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                // Clear frontend data
                this.proxies = [];
                this.renderProxies();
                this.updateProxyStats();
                this.showNotification('✅ Đã xóa tất cả proxy thành công!', 'success');
            } else {
                throw new Error(result.message || 'Failed to clear proxies');
            }

        } catch (error) {
            console.error('Clear all proxies error:', error);
            this.showNotification('❌ Lỗi khi xóa proxy: ' + error.message, 'error');
        }
    }

    getProxyDisplayInfo(account) {
        console.log('🔍 Getting proxy info for account:', account.id, 'proxyId:', account.proxyId);
        console.log('🔍 Available proxies:', this.proxies.length);

        if (!account.proxyId) {
            return '<small class="text-muted">🚫 Không có Proxy</small>';
        }

        // Find proxy info from proxies array
        const proxy = this.proxies.find(p => p.id === account.proxyId);
        console.log('🔍 Found proxy:', proxy);

        if (proxy) {
            const status = proxy.status === 'working' ? '🟢' : proxy.status === 'failed' ? '🔴' : '🟡';
            const country = proxy.country ? this.getCountryFlag(proxy.country) : '🌍';
            return `<small class="text-info">📡 ${proxy.ip}:${proxy.port} ${country} ${status}</small>`;
        } else {
            return `<small class="text-warning">📡 Proxy: ${account.proxyId.substring(0, 8)}... ⚠️</small>`;
        }
    }

    getStatusClass(status) {
        switch(status) {
            case 'logged_in': return 'status-logged-in';
            case 'login_failed': return 'status-error';
            case 'active': return 'status-running';
            default: return 'status-stopped';
        }
    }

    getStatusText(status) {
        switch(status) {
            case 'logged_in': return 'LOGGED IN';
            case 'login_failed': return 'LOGIN FAILED';
            case 'active': return 'ACTIVE';
            case 'inactive': return 'INACTIVE';
            default: return status ? status.toUpperCase() : 'UNKNOWN';
        }
    }

    renderAccounts() {
        console.log('🎨 Rendering accounts:', this.accounts.length);
        console.log('🎨 Accounts data:', this.accounts);
        const grid = document.getElementById('accounts-grid');
        if (!grid) {
            console.error('❌ accounts-grid element not found!');
            return;
        }
        grid.innerHTML = '';

        if (this.accounts.length === 0) {
            console.warn('⚠️ No accounts to render!');
            grid.innerHTML = '<div style="text-align: center; padding: 50px; color: rgba(255,255,255,0.6);">Chưa có tài khoản nào. Hãy thêm tài khoản đầu tiên!</div>';
            return;
        }

        this.accounts.forEach(account => {
            const card = document.createElement('div');
            card.className = 'account-card';
            card.setAttribute('data-account-id', account.id);

            // Determine status display
            const statusText = this.getStatusDisplayText(account.status);
            const statusClass = this.getStatusDisplayClass(account.status);

            card.innerHTML = `
                <div class="account-header">
                    <div class="account-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="account-info">
                        <h4>${account.name || account.email}</h4>
                        <p>${account.email}</p>
                    </div>
                </div>
                <div class="account-status">
                    <span class="status-${account.status || 'inactive'}">${statusText}</span>
                    ${account.browserStatus === 'running' ? '<span class="browser-badge">🌐 BROWSER</span>' : ''}
                    ${account.lastLogin ? `<div class="last-login">Last: ${new Date(account.lastLogin).toLocaleString()}</div>` : ''}
                </div>
                <div class="account-details">
                    <small>ID: ${account.id.substring(0, 8)}...</small>
                    ${this.getProxyDisplayInfo(account)}
                </div>
                <div class="account-actions">
                    ${this.getAccountActionButtons(account)}
                </div>
            `;
            grid.appendChild(card);
        });
    }

    getStatusDisplayText(status) {
        switch (status) {
            case 'logged_in': return '✅ Đã login';
            case 'logging_in': return '🔄 Đang login...';
            case 'login_failed': return '❌ Login thất bại';
            case 'active': return '🟢 Hoạt động';
            case 'inactive': return '⚪ Chưa kích hoạt';
            case 'checking': return '🔍 Đang kiểm tra...';
            case 'error': return '🔴 Lỗi';
            default: return '⚪ Chưa xác định';
        }
    }

    getStatusDisplayClass(status) {
        switch (status) {
            case 'logged_in': return 'status-success';
            case 'logging_in': return 'status-loading';
            case 'login_failed': return 'status-error';
            case 'active': return 'status-active';
            case 'inactive': return 'status-inactive';
            case 'checking': return 'status-loading';
            case 'error': return 'status-error';
            default: return 'status-inactive';
        }
    }

    getAccountActionButtons(account) {
        const status = account.status || 'inactive';
        const browserRunning = account.browserStatus === 'running';

        switch (status) {
            case 'logging_in':
                return `
                    <button class="btn btn-sm btn-secondary" disabled>
                        <i class="fas fa-spinner fa-spin"></i> Đang login...
                    </button>
                `;

            case 'logged_in':
                return `
                    <button class="btn btn-success btn-sm" onclick="app.launchBrowser('${account.id}')" title="Mở Browser" ${browserRunning ? 'disabled' : ''}>
                        <i class="fas fa-rocket"></i>
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="app.logoutAccount('${account.id}')" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                    <button class="btn btn-info btn-sm" onclick="app.checkSingleAccount('${account.id}')" title="Kiểm tra">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="app.removeAccount('${account.id}')" title="Xóa">
                        <i class="fas fa-trash"></i>
                    </button>
                `;

            case 'login_failed':
                return `
                    <button class="btn btn-primary btn-sm" onclick="app.loginFacebookAccount('${account.id}')" title="Thử lại Login">
                        <i class="fas fa-redo"></i> Thử lại
                    </button>
                    <button class="btn btn-info btn-sm" onclick="app.checkSingleAccount('${account.id}')" title="Kiểm tra">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="app.removeAccount('${account.id}')" title="Xóa">
                        <i class="fas fa-trash"></i>
                    </button>
                `;

            default:
                return `
                    <button class="btn btn-success btn-sm" onclick="app.launchBrowser('${account.id}')" title="Mở Browser" ${browserRunning ? 'disabled' : ''}>
                        <i class="fas fa-rocket"></i>
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="app.loginFacebookAccount('${account.id}')" title="Login Facebook" ${!browserRunning ? 'disabled' : ''}>
                        <i class="fab fa-facebook-f"></i>
                    </button>
                    <button class="btn btn-info btn-sm" onclick="app.checkSingleAccount('${account.id}')" title="Kiểm tra">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="app.editAccount('${account.id}')" title="Chỉnh sửa">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="app.removeAccount('${account.id}')" title="Xóa">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
        }
    }

    async loadData() {
        try {
            console.log('🔄 Loading data...');

            // Load proxies FIRST
            const proxiesResponse = await fetch('/api/proxies');
            const proxiesData = await proxiesResponse.json();
            console.log('📊 Proxies data:', proxiesData);
            if (proxiesData.success) {
                this.proxies = proxiesData.proxies;
                console.log('✅ Loaded proxies:', this.proxies.length);
            }

            // Then load accounts
            const accountsResponse = await fetch('/api/accounts');
            const accountsData = await accountsResponse.json();
            console.log('📊 Accounts data:', accountsData);
            if (accountsData.success) {
                this.accounts = accountsData.accounts;
                console.log('✅ Loaded accounts:', this.accounts.length);

                // Auto-assign proxies to accounts that don't have one
                if (this.accounts.length > 0) {
                    console.log('🤖 Auto-assigning proxies to accounts...');
                    this.accounts = await this.assignProxiesToAccounts(this.accounts);
                }

                this.renderAccounts();
            }

            // Load tasks
            const tasksResponse = await fetch('/api/tasks');
            const tasksData = await tasksResponse.json();
            if (tasksData.success) {
                this.tasks = tasksData.tasks;
                // this.renderTasks(); // TODO: Implement renderTasks function
            }

            // Proxies already loaded above, just render them
            if (this.proxies && this.proxies.length > 0) {
                this.renderProxies();
                this.updateProxyStats();
            }

            // Load settings
            const settingsResponse = await fetch('/api/settings');
            const settingsData = await settingsResponse.json();
            if (settingsData.success) {
                this.settings = { ...this.settings, ...settingsData.settings };
                this.loadSettingsToUI();
            }

            // Load logs
            this.refreshLogs();

            this.updateStats();

        } catch (error) {
            console.error('Load data error:', error);
        }
    }

    updateStats() {
        this.stats.totalAccounts = this.accounts.length;
        this.stats.activeAccounts = this.accounts.filter(acc => acc.status === 'active').length;
        this.stats.runningTasks = this.tasks.filter(task => task.status === 'running').length;
        
        // Calculate success rate
        const completedTasks = this.tasks.filter(task => task.status === 'completed');
        this.stats.successRate = this.tasks.length > 0 ? 
            Math.round((completedTasks.length / this.tasks.length) * 100) : 0;

        // Update UI
        document.getElementById('total-accounts').textContent = this.stats.totalAccounts;
        document.getElementById('active-accounts').textContent = this.stats.activeAccounts;
        document.getElementById('running-tasks').textContent = this.stats.runningTasks;
        document.getElementById('success-rate').textContent = this.stats.successRate + '%';
    }

    startStatsUpdater() {
        setInterval(() => {
            this.updateTaskProgress();
        }, 2000);
    }

    async updateTaskProgress() {
        if (this.tasks.length === 0) return;

        try {
            const response = await fetch('/api/tasks/progress');
            const data = await response.json();
            
            if (data.success) {
                data.tasks.forEach(taskUpdate => {
                    const task = this.tasks.find(t => t.id === taskUpdate.id);
                    if (task) {
                        task.progress = taskUpdate.progress;
                        task.status = taskUpdate.status;
                        
                        // Update UI
                        const row = document.querySelector(`tr[data-task-id="${task.id}"]`);
                        if (row) {
                            const progressFill = row.querySelector('.progress-fill');
                            const progressText = row.querySelector('small');
                            const statusBadge = row.querySelector('.status-badge:last-child');
                            
                            progressFill.style.width = task.progress + '%';
                            progressText.textContent = task.progress + '%';
                            statusBadge.textContent = task.status.toUpperCase();
                            statusBadge.className = `status-badge status-${task.status}`;
                        }

                        // Update automation card counts
                        const countSpan = document.getElementById(`${task.type}-count`);
                        if (countSpan && taskUpdate.completed) {
                            countSpan.textContent = taskUpdate.completed;
                        }
                    }
                });
            }
        } catch (error) {
            console.error('Update progress error:', error);
        }
    }

    showModal(modalId) {
        // Load proxies if opening add-account modal
        if (modalId === 'add-account-modal') {
            this.loadProxiesForSelect();
        }
        document.getElementById(modalId).style.display = 'block';
    }

    async loadProxiesForSelect() {
        try {
            const response = await fetch('/api/proxies');
            const data = await response.json();

            const select = document.getElementById('account-proxy-select');
            // Clear existing options
            select.innerHTML = '<option value="">Không Proxy</option><option value="auto">🤖 Tự Động Gán Proxy</option>';

            if (data.success && data.proxies) {
                // Show available proxies (not assigned to any account)
                const availableProxies = await this.getAvailableProxies(data.proxies);

                availableProxies.forEach(proxy => {
                    const option = document.createElement('option');
                    option.value = proxy.id;
                    option.textContent = `${proxy.ip}:${proxy.port} (${proxy.country || 'Unknown'})`;
                    select.appendChild(option);
                });

                // Show assigned proxies (disabled)
                const assignedProxies = data.proxies.filter(p => !availableProxies.find(ap => ap.id === p.id));
                if (assignedProxies.length > 0) {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = '--- Đã Được Gán ---';
                    assignedProxies.forEach(proxy => {
                        const option = document.createElement('option');
                        option.value = proxy.id;
                        option.textContent = `${proxy.ip}:${proxy.port} (Đã gán)`;
                        option.disabled = true;
                        optgroup.appendChild(option);
                    });
                    select.appendChild(optgroup);
                }
            }
        } catch (error) {
            console.error('Error loading proxies:', error);
        }
    }

    async getAvailableProxies(allProxies) {
        // Get list of proxy IDs already assigned to accounts
        const assignedProxyIds = this.accounts
            .filter(acc => acc.proxyId)
            .map(acc => acc.proxyId);

        // Return proxies not assigned to any account
        return allProxies.filter(proxy => !assignedProxyIds.includes(proxy.id));
    }

    hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    clearForm(formId) {
        document.getElementById(formId).reset();
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${this.getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.parentNode.removeChild(notification);
        });
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    getActionName(action) {
        const actionNames = {
            'like': 'Like',
            'comment': 'Comment',
            'follow': 'Follow',
            'share': 'Share'
        };
        return actionNames[action] || action;
    }

    getStatusText(status) {
        const statusTexts = {
            'active': 'Đang hoạt động',
            'inactive': 'Không hoạt động',
            'running': 'Đang chạy',
            'paused': 'Tạm dừng',
            'completed': 'Hoàn thành',
            'failed': 'Thất bại',
            'working': 'Hoạt động',
            'untested': 'Chưa test'
        };
        return statusTexts[status] || status;
    }

    // Placeholder methods for additional functionality
    async pauseAllTasks() {
        this.showNotification('All tasks paused', 'info');
    }

    async resumeAllTasks() {
        this.showNotification('All tasks resumed', 'info');
    }

    async checkAllAccounts() {
        this.showNotification('Đang kiểm tra tất cả tài khoản...', 'info');

        try {
            // Gọi API check all accounts thật
            const response = await fetch('/api/accounts/check-all', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (result.success) {
                // Cập nhật accounts với kết quả thật
                for (const checkResult of result.results) {
                    const account = this.accounts.find(acc => acc.id === checkResult.account.id);
                    if (account) {
                        account.status = checkResult.result.status;
                        account.lastChecked = checkResult.result.lastChecked;
                        account.name = checkResult.result.profileName || account.name;
                        account.notes = checkResult.result.details;
                    }
                }

                this.renderAccounts();
                this.updateStats();

                const { total, success, failed } = result.summary;
                this.showNotification(`Đã kiểm tra ${total} tài khoản: ${success} thành công, ${failed} thất bại`, 'success');
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification('Lỗi khi kiểm tra tài khoản: ' + error.message, 'error');
        }
    }

    async checkSingleAccount(accountId) {
        this.showNotification('Đang kiểm tra tài khoản...', 'info');

        try {
            // Gọi API check single account thật
            const response = await fetch(`/api/accounts/check/${accountId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (result.success) {
                // Cập nhật account với kết quả thật
                const account = this.accounts.find(acc => acc.id === accountId);
                if (account) {
                    account.status = result.result.status;
                    account.lastChecked = result.result.lastChecked;
                    account.name = result.result.profileName || account.name;
                    account.notes = result.result.details;
                }

                this.renderAccounts();
                this.updateStats();

                this.showNotification(result.message, result.result.success ? 'success' : 'error');
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification('Lỗi khi kiểm tra tài khoản: ' + error.message, 'error');
        }
    }

    async pauseTask(taskId) {
        this.showNotification('Task paused', 'info');
    }

    async stopTask(taskId) {
        this.removeTaskFromTable(taskId);
        this.showNotification('Task stopped', 'info');
    }

    async launchBrowser(accountId) {
        try {
            this.showNotification('Đang khởi động trình duyệt...', 'info');

            const response = await fetch(`/api/accounts/${accountId}/launch-browser`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                this.showNotification('Trình duyệt đã được khởi động!', 'success');
                // Update account status to show browser is running
                const account = this.accounts.find(acc => acc.id === accountId);
                if (account) {
                    account.browserStatus = 'running';
                    this.renderAccounts();
                }
            } else {
                throw new Error(result.message || 'Failed to launch browser');
            }
        } catch (error) {
            console.error('Launch browser error:', error);
            this.showNotification('Lỗi khi khởi động trình duyệt: ' + error.message, 'error');
        }
    }

    async launchBrowser(accountId) {
        try {
            this.showNotification('Đang khởi động trình duyệt...', 'info');

            const response = await fetch(`/api/accounts/${accountId}/launch-browser`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                this.showNotification('Trình duyệt đã được khởi động!', 'success');
                // Update account status
                const account = this.accounts.find(acc => acc.id === accountId);
                if (account) {
                    account.browserStatus = 'running';
                    this.renderAccounts();
                }
            } else {
                throw new Error(result.message || 'Failed to launch browser');
            }
        } catch (error) {
            console.error('Launch browser error:', error);
            this.showNotification('Lỗi khi khởi động trình duyệt: ' + error.message, 'error');
        }
    }

    async closeBrowser(accountId) {
        try {
            this.showNotification('Đang đóng trình duyệt...', 'info');

            const response = await fetch(`/api/accounts/${accountId}/close-browser`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                this.showNotification('Trình duyệt đã được đóng!', 'success');
                // Update account status
                const account = this.accounts.find(acc => acc.id === accountId);
                if (account) {
                    account.browserStatus = 'stopped';
                    this.renderAccounts();
                }
            } else {
                throw new Error(result.message || 'Failed to close browser');
            }
        } catch (error) {
            console.error('Close browser error:', error);
            this.showNotification('Lỗi khi đóng trình duyệt: ' + error.message, 'error');
        }
    }

    async loginFacebook(accountId) {
        try {
            this.showNotification('Đang đăng nhập Facebook...', 'info');

            const response = await fetch(`/api/accounts/${accountId}/login-facebook`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                this.showNotification('Đăng nhập Facebook thành công!', 'success');
                // Refresh accounts from server to get updated status
                await this.loadAccounts();
            } else {
                throw new Error(result.message || 'Failed to login Facebook');
            }
        } catch (error) {
            console.error('Facebook login error:', error);
            this.showNotification('Lỗi khi đăng nhập Facebook: ' + error.message, 'error');
        }
    }

    async autoLoginFacebook(accountId) {
        try {
            this.showNotification('🚀 Bắt đầu Auto Login Facebook (Full Process)...', 'info');

            // Step 1: Launch browser first
            this.showNotification('🌐 Bước 1: Khởi động trình duyệt...', 'info');
            const launchResponse = await fetch(`/api/accounts/${accountId}/launch-browser`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const launchResult = await launchResponse.json();
            if (!launchResult.success) {
                throw new Error('Failed to launch browser: ' + launchResult.message);
            }

            // Wait a bit for browser to fully load
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Step 2: Login to Facebook
            this.showNotification('🔐 Bước 2: Đăng nhập Facebook...', 'info');
            const loginResponse = await fetch(`/api/accounts/${accountId}/login-facebook`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const loginResult = await loginResponse.json();
            if (loginResult.success) {
                this.showNotification('✅ Auto Login Facebook hoàn tất thành công!', 'success');
                // Refresh accounts from server to get updated status
                await this.loadAccounts();
            } else {
                throw new Error(loginResult.message || 'Auto login failed');
            }

        } catch (error) {
            console.error('Auto Login Facebook error:', error);
            this.showNotification('❌ Lỗi Auto Login Facebook: ' + error.message, 'error');
        }
    }

    async checkAccount(accountId) {
        this.showNotification('Checking account...', 'info');
    }

    async removeAccount(accountId) {
        if (!confirm('Bạn có chắc chắn muốn xóa tài khoản này?')) {
            return;
        }

        try {
            this.showNotification('Đang xóa tài khoản...', 'info');

            const response = await fetch(`/api/accounts/${accountId}`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                // Remove from frontend array
                this.accounts = this.accounts.filter(acc => acc.id !== accountId);
                this.renderAccounts();
                this.updateStats();
                this.showNotification('Đã xóa tài khoản thành công!', 'success');
            } else {
                throw new Error(result.message || 'Failed to delete account');
            }
        } catch (error) {
            console.error('Delete account error:', error);
            this.showNotification('Lỗi khi xóa tài khoản: ' + error.message, 'error');
        }
    }

    showBulkAddModal() {
        // Create bulk add modal dynamically
        const modal = document.createElement('div');
        modal.id = 'bulk-add-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Bulk Add Accounts</h3>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <p>Paste accounts in format: email|password|2fa (one per line)</p>
                    <textarea id="bulk-accounts-text" rows="10" style="width: 100%; padding: 10px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 5px; color: white; resize: vertical;"></textarea>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="app.hideBulkAddModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.processBulkAdd()">Add Accounts</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'block';
        
        // Close functionality
        modal.querySelector('.close').addEventListener('click', () => {
            this.hideBulkAddModal();
        });
    }

    hideBulkAddModal() {
        const modal = document.getElementById('bulk-add-modal');
        if (modal) {
            modal.remove();
        }
    }

    async processBulkAdd() {
        const text = document.getElementById('bulk-accounts-text').value;
        const lines = text.split('\n').filter(line => line.trim());

        if (lines.length === 0) {
            this.showNotification('Vui lòng nhập danh sách tài khoản!', 'error');
            return;
        }

        const accounts = [];
        for (const line of lines) {
            const parts = line.split('|');
            if (parts.length >= 2) {
                accounts.push({
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    twoFA: parts[2] ? parts[2].trim() : '',
                    name: parts[0].split('@')[0]
                });
            }
        }

        if (accounts.length === 0) {
            this.showNotification('Không có tài khoản hợp lệ nào!', 'error');
            return;
        }

        try {
            this.showNotification(`Đang thêm ${accounts.length} tài khoản với auto-proxy...`, 'info');

            // Auto-assign proxies to accounts
            const accountsWithProxy = await this.assignProxiesToAccounts(accounts);

            // Gọi API bulk add accounts
            const response = await fetch('/api/accounts/bulk-add', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ accounts: accountsWithProxy })
            });

            const result = await response.json();
            if (result.success) {
                // Reload data from server to ensure sync
                await this.loadData();
                this.hideBulkAddModal();

                const withProxyCount = accountsWithProxy.filter(acc => acc.proxyId).length;
                const withoutProxyCount = accountsWithProxy.length - withProxyCount;

                let message = `✅ Đã thêm ${result.accounts.length} tài khoản thành công!`;
                if (withProxyCount > 0) {
                    message += `\n🌐 ${withProxyCount} tài khoản có proxy`;
                }
                if (withoutProxyCount > 0) {
                    message += `\n🚫 ${withoutProxyCount} tài khoản không có proxy (hết proxy khả dụng)`;
                }

                this.showNotification(message, 'success');
            } else {
                throw new Error(result.message || 'Failed to add accounts');
            }

        } catch (error) {
            console.error('Bulk add accounts error:', error);
            this.showNotification('Lỗi khi thêm tài khoản: ' + error.message, 'error');
        }
    }

    async assignProxiesToAccounts(accounts) {
        try {
            // Get all available proxies
            const response = await fetch('/api/proxies');
            const data = await response.json();

            if (!data.success || !data.proxies || data.proxies.length === 0) {
                console.log('⚠️ No proxies available for auto-assignment');
                return accounts; // Return accounts without proxy assignment
            }

            // Get available proxies (not assigned to any account)
            const availableProxies = await this.getAvailableProxies(data.proxies);

            if (availableProxies.length === 0) {
                console.log('⚠️ All proxies are already assigned');
                return accounts; // Return accounts without proxy assignment
            }

            console.log(`🤖 Auto-assigning ${Math.min(accounts.length, availableProxies.length)} proxies to accounts`);

            // Assign proxies to accounts (round-robin style)
            const accountsWithProxy = accounts.map((account, index) => {
                if (index < availableProxies.length) {
                    return {
                        ...account,
                        proxyId: availableProxies[index].id
                    };
                } else {
                    // No more proxies available
                    return account;
                }
            });

            return accountsWithProxy;

        } catch (error) {
            console.error('Error assigning proxies to accounts:', error);
            return accounts; // Return accounts without proxy assignment on error
        }
    }

    // Proxy Management Methods
    showBulkProxiesModal() {
        const modal = document.createElement('div');
        modal.id = 'bulk-add-proxies-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Bulk Add Proxies</h3>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <p>Paste proxies in format: <code>ip:port:user:pass</code> or <code>ip:port</code> (one per line)</p>
                    <textarea id="bulk-proxies-text" rows="15" placeholder="Example:
***********:8080:username:password
***********:8080
proxy.example.com:3128:user:pass"></textarea>
                    <div class="form-group">
                        <label>Proxy Type:</label>
                        <select id="proxy-type">
                            <option value="http">HTTP</option>
                            <option value="https">HTTPS</option>
                            <option value="socks4">SOCKS4</option>
                            <option value="socks5">SOCKS5</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="app.hideBulkProxiesModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="app.processBulkProxies()">Add Proxies</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        modal.querySelector('.close').addEventListener('click', () => {
            this.hideBulkProxiesModal();
        });
    }

    hideBulkProxiesModal() {
        const modal = document.getElementById('bulk-add-proxies-modal');
        if (modal) {
            modal.remove();
        }
    }

    async processBulkProxies() {
        const text = document.getElementById('bulk-proxies-text').value;
        const type = document.getElementById('proxy-type').value;
        const lines = text.split('\n').filter(line => line.trim());

        if (lines.length === 0) {
            this.showNotification('Vui lòng nhập danh sách proxy', 'error');
            return;
        }

        const proxies = [];
        for (const line of lines) {
            const parts = line.split(':');
            if (parts.length >= 2) {
                const proxy = {
                    ip: parts[0].trim(),
                    port: parseInt(parts[1].trim()),
                    username: parts[2] ? parts[2].trim() : '',
                    password: parts[3] ? parts[3].trim() : '',
                    type: type
                };
                proxies.push(proxy);
            }
        }

        if (proxies.length === 0) {
            this.showNotification('Không có proxy hợp lệ nào được tìm thấy', 'error');
            return;
        }

        try {
            this.showNotification(`Đang thêm ${proxies.length} proxy...`, 'info');

            // Gọi API bulk add proxies
            const response = await fetch('/api/proxies/bulk-add', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ proxies })
            });

            const result = await response.json();

            if (result.success) {
                // Thêm proxies mới vào danh sách
                for (const proxyResult of result.results) {
                    if (proxyResult.success) {
                        this.proxies.push(proxyResult.proxy);
                    }
                }

                this.renderProxies();
                this.updateProxyStats();
                this.hideBulkProxiesModal();

                const { total, added, failed } = result.summary;
                this.showNotification(`Đã thêm ${added}/${total} proxy thành công!`, 'success');
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification('Lỗi khi thêm proxy: ' + error.message, 'error');
        }
    }

    renderProxies() {
        const tbody = document.getElementById('proxies-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.proxies.forEach(proxy => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${proxy.ip}:${proxy.port}</td>
                <td>
                    <span class="country-flag">${this.getCountryFlag(proxy.country)}</span>
                    ${proxy.country}
                </td>
                <td><span class="proxy-type">${proxy.type.toUpperCase()}</span></td>
                <td><span class="proxy-status-${proxy.status}">${proxy.status.toUpperCase()}</span></td>
                <td>
                    <div class="speed-indicator">
                        <div class="speed-bar">
                            <div class="speed-fill speed-${this.getSpeedClass(proxy.speed)}" style="width: ${proxy.speed}%"></div>
                        </div>
                        <span>${proxy.speed}ms</span>
                    </div>
                </td>
                <td>${proxy.lastTested ? new Date(proxy.lastTested).toLocaleString() : 'Never'}</td>
                <td>
                    <button class="btn btn-secondary btn-sm" onclick="app.testProxy('${proxy.id}')">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="app.removeProxy('${proxy.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateProxyStats() {
        const total = this.proxies.length;
        const working = this.proxies.filter(p => p.status === 'working').length;
        const failed = this.proxies.filter(p => p.status === 'failed').length;
        const countries = [...new Set(this.proxies.map(p => p.country))].length;

        document.getElementById('total-proxies').textContent = total;
        document.getElementById('working-proxies').textContent = working;
        document.getElementById('failed-proxies').textContent = failed;
        document.getElementById('proxy-countries').textContent = countries;
    }

    getCountryFlag(country) {
        const flags = {
            'US': '🇺🇸', 'UK': '🇬🇧', 'DE': '🇩🇪', 'FR': '🇫🇷', 'JP': '🇯🇵',
            'CA': '🇨🇦', 'AU': '🇦🇺', 'NL': '🇳🇱', 'SG': '🇸🇬', 'Unknown': '🌍'
        };
        return flags[country] || '🌍';
    }

    getSpeedClass(speed) {
        if (speed < 200) return 'fast';
        if (speed < 500) return 'medium';
        return 'slow';
    }

    async testProxy(proxyId) {
        const proxy = this.proxies.find(p => p.id === proxyId);
        if (!proxy) return;

        proxy.status = 'testing';
        this.renderProxies();
        this.showNotification('Đang test proxy...', 'info');

        try {
            // Gọi API test proxy thật
            const response = await fetch(`/api/proxies/test/${proxyId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    ip: proxy.ip,
                    port: proxy.port,
                    username: proxy.username,
                    password: proxy.password,
                    type: proxy.type
                })
            });

            const result = await response.json();

            if (result.success) {
                // Cập nhật proxy với kết quả thật
                proxy.status = result.result.status;
                proxy.speed = result.result.speed;
                proxy.country = result.result.country;
                proxy.lastTested = result.result.lastTested;
                proxy.responseIP = result.result.responseIP;

                this.renderProxies();
                this.updateProxyStats();
                this.showNotification(result.message, result.result.success ? 'success' : 'error');
            } else {
                proxy.status = 'failed';
                this.renderProxies();
                this.updateProxyStats();
                throw new Error(result.message);
            }

        } catch (error) {
            proxy.status = 'failed';
            this.renderProxies();
            this.updateProxyStats();
            this.showNotification('Lỗi khi test proxy: ' + error.message, 'error');
        }
    }

    async testAllProxies() {
        this.showNotification('Đang test tất cả proxy...', 'info');

        try {
            // Gọi API test all proxies thật
            const response = await fetch('/api/proxies/test-all', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    proxies: this.proxies
                })
            });

            const result = await response.json();

            if (result.success) {
                // Cập nhật proxies với kết quả thật
                for (const testResult of result.results) {
                    const proxy = this.proxies.find(p => p.id === testResult.proxy.id);
                    if (proxy) {
                        proxy.status = testResult.result.status;
                        proxy.speed = testResult.result.speed;
                        proxy.country = testResult.result.country;
                        proxy.lastTested = testResult.result.lastTested;
                        proxy.responseIP = testResult.result.responseIP;
                    }
                }

                this.renderProxies();
                this.updateProxyStats();

                const { total, working, failed } = result.summary;
                this.showNotification(`Đã test ${total} proxy: ${working} hoạt động, ${failed} lỗi`, 'success');
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification('Lỗi khi test proxy: ' + error.message, 'error');
        }
    }

    async removeProxy(proxyId) {
        if (!confirm('Bạn có chắc chắn muốn xóa proxy này?')) {
            return;
        }

        try {
            this.showNotification('Đang xóa proxy...', 'info');

            const response = await fetch(`/api/proxies/${proxyId}`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            if (result.success) {
                // Remove from frontend array
                this.proxies = this.proxies.filter(p => p.id !== proxyId);
                this.renderProxies();
                this.updateProxyStats();
                this.showNotification('Đã xóa proxy thành công!', 'success');
            } else {
                throw new Error(result.message || 'Failed to delete proxy');
            }
        } catch (error) {
            console.error('Delete proxy error:', error);
            this.showNotification('Lỗi khi xóa proxy: ' + error.message, 'error');
        }
    }

    // Settings Management Methods
    loadSettingsToUI() {
        // Load settings values to form inputs
        document.getElementById('delay-between-actions').value = this.settings.delayBetweenActions;
        document.getElementById('max-actions-session').value = this.settings.maxActionsSession;
        document.getElementById('session-break').value = this.settings.sessionBreak;
        document.getElementById('randomize-delays').checked = this.settings.randomizeDelays;
        document.getElementById('headless-mode').checked = this.settings.headlessMode;
        document.getElementById('disable-images').checked = this.settings.disableImages;
        document.getElementById('max-browsers').value = this.settings.maxBrowsers;
        document.getElementById('browser-timeout').value = this.settings.browserTimeout;
        document.getElementById('auto-proxy-rotation').checked = this.settings.autoProxyRotation;
        document.getElementById('fingerprint-spoofing').checked = this.settings.fingerprintSpoofing;
        document.getElementById('clear-cookies').checked = this.settings.clearCookies;
        document.getElementById('user-agent-rotation').value = this.settings.userAgentRotation;
        document.getElementById('comment-templates').value = this.settings.commentTemplates.join('\n');
        document.getElementById('random-comments').checked = this.settings.randomComments;
        document.getElementById('desktop-notifications').checked = this.settings.desktopNotifications;
        document.getElementById('sound-notifications').checked = this.settings.soundNotifications;
        document.getElementById('email-notifications').checked = this.settings.emailNotifications;
        document.getElementById('notification-email').value = this.settings.notificationEmail;
        document.getElementById('log-level').value = this.settings.logLevel;
        document.getElementById('auto-backup').checked = this.settings.autoBackup;
        document.getElementById('auto-update-check').checked = this.settings.autoUpdateCheck;
        document.getElementById('api-rate-limit').value = this.settings.apiRateLimit;
    }

    async saveSettings() {
        // Collect settings from form
        this.settings.delayBetweenActions = parseInt(document.getElementById('delay-between-actions').value);
        this.settings.maxActionsSession = parseInt(document.getElementById('max-actions-session').value);
        this.settings.sessionBreak = parseInt(document.getElementById('session-break').value);
        this.settings.randomizeDelays = document.getElementById('randomize-delays').checked;
        this.settings.headlessMode = document.getElementById('headless-mode').checked;
        this.settings.disableImages = document.getElementById('disable-images').checked;
        this.settings.maxBrowsers = parseInt(document.getElementById('max-browsers').value);
        this.settings.browserTimeout = parseInt(document.getElementById('browser-timeout').value);
        this.settings.autoProxyRotation = document.getElementById('auto-proxy-rotation').checked;
        this.settings.fingerprintSpoofing = document.getElementById('fingerprint-spoofing').checked;
        this.settings.clearCookies = document.getElementById('clear-cookies').checked;
        this.settings.userAgentRotation = document.getElementById('user-agent-rotation').value;
        this.settings.commentTemplates = document.getElementById('comment-templates').value.split('\n').filter(line => line.trim());
        this.settings.randomComments = document.getElementById('random-comments').checked;
        this.settings.desktopNotifications = document.getElementById('desktop-notifications').checked;
        this.settings.soundNotifications = document.getElementById('sound-notifications').checked;
        this.settings.emailNotifications = document.getElementById('email-notifications').checked;
        this.settings.notificationEmail = document.getElementById('notification-email').value;
        this.settings.logLevel = document.getElementById('log-level').value;
        this.settings.autoBackup = document.getElementById('auto-backup').checked;
        this.settings.autoUpdateCheck = document.getElementById('auto-update-check').checked;
        this.settings.apiRateLimit = parseInt(document.getElementById('api-rate-limit').value);

        try {
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(this.settings)
            });

            const result = await response.json();
            if (result.success) {
                this.showNotification('Đã lưu cài đặt thành công!', 'success');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            this.showNotification('Lỗi khi lưu cài đặt: ' + error.message, 'error');
        }
    }

    resetSettings() {
        if (confirm('Bạn có chắc chắn muốn khôi phục tất cả cài đặt về mặc định?')) {
            // Reset to default values
            this.settings = {
                delayBetweenActions: 3000,
                maxActionsSession: 50,
                sessionBreak: 15,
                randomizeDelays: true,
                headlessMode: false,
                disableImages: true,
                maxBrowsers: 5,
                browserTimeout: 30,
                autoProxyRotation: true,
                fingerprintSpoofing: true,
                clearCookies: false,
                userAgentRotation: 'random',
                commentTemplates: [
                    'Great post! 👍',
                    'Thanks for sharing!',
                    'Interesting! 🤔',
                    'Love this! ❤️',
                    'Amazing content!',
                    'Nice work! 🔥'
                ],
                randomComments: true,
                desktopNotifications: true,
                soundNotifications: false,
                emailNotifications: false,
                notificationEmail: '',
                logLevel: 'info',
                autoBackup: true,
                autoUpdateCheck: true,
                apiRateLimit: 60
            };

            this.loadSettingsToUI();
            this.showNotification('Đã khôi phục cài đặt về mặc định', 'info');
        }
    }

    // Logs Management Methods
    async refreshLogs() {
        try {
            const level = document.getElementById('log-level-filter')?.value || 'all';
            const response = await fetch(`/api/logs?level=${level}&limit=100`);
            const data = await response.json();

            if (data.success) {
                this.logs = data.logs;
                this.renderLogs();
            }
        } catch (error) {
            console.error('Refresh logs error:', error);
        }
    }

    renderLogs() {
        const container = document.getElementById('logs-container');
        if (!container) return;

        container.innerHTML = '';

        this.logs.forEach(log => {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${log.level}`;

            const timestamp = new Date(log.timestamp).toLocaleTimeString();

            logEntry.innerHTML = `
                <span class="log-timestamp">${timestamp}</span>
                <span class="log-level ${log.level}">${log.level}</span>
                <span class="log-message">${log.message}</span>
                ${log.data && Object.keys(log.data).length > 0 ?
                    `<div class="log-data">${JSON.stringify(log.data, null, 2)}</div>` : ''}
            `;

            container.appendChild(logEntry);
        });

        // Auto scroll to bottom
        container.scrollTop = container.scrollHeight;
    }

    filterLogs(level) {
        this.refreshLogs();
    }

    async clearLogs() {
        if (confirm('Bạn có chắc chắn muốn xóa tất cả nhật ký?')) {
            try {
                const response = await fetch('/api/logs/clear', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    this.logs = [];
                    this.renderLogs();
                    this.showNotification('Đã xóa nhật ký thành công', 'success');
                }
            } catch (error) {
                this.showNotification('Lỗi khi xóa nhật ký: ' + error.message, 'error');
            }
        }
    }

    async exportLogs() {
        try {
            const response = await fetch('/api/logs/export', { method: 'POST' });
            const blob = await response.blob();

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `fb-nexus-logs-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.showNotification('Đã xuất nhật ký thành công', 'success');
        } catch (error) {
            this.showNotification('Lỗi khi xuất nhật ký: ' + error.message, 'error');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new FBNexusApp();
});

// Add notification styles
const notificationStyles = `
<style>
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 3000;
    min-width: 300px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideIn 0.3s ease;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-warning {
    border-left: 4px solid #ffc107;
}

.notification-info {
    border-left: 4px solid #17a2b8;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', notificationStyles);
