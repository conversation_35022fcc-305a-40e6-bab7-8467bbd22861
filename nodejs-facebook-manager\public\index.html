<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FB NEXUS - Facebook Account Manager</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <i class="fab fa-facebook"></i>
                <span>FB NEXUS</span>
            </div>
            <nav class="nav-menu">
                <a href="#dashboard" class="nav-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tổng Quan</span>
                </a>
                <a href="#accounts" class="nav-item" data-page="accounts">
                    <i class="fas fa-users"></i>
                    <span>T<PERSON><PERSON></span>
                </a>
                <a href="#proxies" class="nav-item" data-page="proxies">
                    <i class="fas fa-shield-alt"></i>
                    <span>Proxy</span>
                </a>
                <a href="#automation" class="nav-item" data-page="automation">
                    <i class="fas fa-robot"></i>
                    <span>Tự Động Hóa</span>
                </a>
                <a href="#logs" class="nav-item" data-page="logs">
                    <i class="fas fa-file-alt"></i>
                    <span>Nhật Ký</span>
                </a>
                <a href="#settings" class="nav-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>Cài Đặt</span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <h1 id="page-title">Automation</h1>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" id="add-account-btn">
                        <i class="fas fa-plus"></i>
                        Thêm Tài Khoản
                    </button>
                    <div class="user-menu">
                        <i class="fas fa-user-circle"></i>
                        <span>Quản Trị</span>
                    </div>
                </div>
            </div>

            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-accounts">0</h3>
                            <p>Tổng Tài Khoản</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="active-accounts">0</h3>
                            <p>Đang Hoạt Động</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="running-tasks">0</h3>
                            <p>Tác Vụ Đang Chạy</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="success-rate">0%</h3>
                            <p>Tỷ Lệ Thành Công</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Automation Page -->
            <div id="automation-page" class="page-content active">
                <!-- Automation Cards -->
                <div class="automation-grid">
                    <div class="automation-card" data-action="like">
                        <div class="card-header">
                            <i class="fas fa-heart"></i>
                            <h3>Tự Động Like</h3>
                        </div>
                        <div class="card-body">
                            <p>Tự động like bài viết từ các trang mục tiêu</p>
                            <div class="card-stats">
                                <span>Trạng thái: <span class="status-inactive">Không hoạt động</span></span>
                                <span>Đã hoàn thành: <span id="like-count">0</span></span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary start-automation" data-action="like">
                                <i class="fas fa-play"></i>
                                Bắt Đầu Auto Like
                            </button>
                        </div>
                    </div>

                    <div class="automation-card" data-action="comment">
                        <div class="card-header">
                            <i class="fas fa-comment"></i>
                            <h3>Tự Động Comment</h3>
                        </div>
                        <div class="card-body">
                            <p>Tự động comment bài viết với tin nhắn tùy chỉnh</p>
                            <div class="card-stats">
                                <span>Trạng thái: <span class="status-inactive">Không hoạt động</span></span>
                                <span>Đã hoàn thành: <span id="comment-count">0</span></span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary start-automation" data-action="comment">
                                <i class="fas fa-play"></i>
                                Bắt Đầu Auto Comment
                            </button>
                        </div>
                    </div>

                    <div class="automation-card" data-action="follow">
                        <div class="card-header">
                            <i class="fas fa-user-plus"></i>
                            <h3>Tự Động Follow</h3>
                        </div>
                        <div class="card-body">
                            <p>Tự động theo dõi người dùng và trang</p>
                            <div class="card-stats">
                                <span>Trạng thái: <span class="status-inactive">Không hoạt động</span></span>
                                <span>Đã hoàn thành: <span id="follow-count">0</span></span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary start-automation" data-action="follow">
                                <i class="fas fa-play"></i>
                                Bắt Đầu Auto Follow
                            </button>
                        </div>
                    </div>

                    <div class="automation-card" data-action="share">
                        <div class="card-header">
                            <i class="fas fa-share"></i>
                            <h3>Tự Động Share</h3>
                        </div>
                        <div class="card-body">
                            <p>Tự động chia sẻ bài viết lên timeline hoặc nhóm</p>
                            <div class="card-stats">
                                <span>Trạng thái: <span class="status-inactive">Không hoạt động</span></span>
                                <span>Đã hoàn thành: <span id="share-count">0</span></span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary start-automation" data-action="share">
                                <i class="fas fa-play"></i>
                                Bắt Đầu Auto Share
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Automation Tasks Table -->
                <div class="tasks-section">
                    <div class="section-header">
                        <h2>Tác Vụ Tự Động Hóa</h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" id="pause-all-btn">
                                <i class="fas fa-pause"></i>
                                Tạm Dừng Tất Cả
                            </button>
                            <button class="btn btn-secondary" id="resume-all-btn">
                                <i class="fas fa-play"></i>
                                Tiếp Tục Tất Cả
                            </button>
                        </div>
                    </div>
                    <div class="tasks-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Loại Tác Vụ</th>
                                    <th>Tài Khoản</th>
                                    <th>Mục Tiêu</th>
                                    <th>Trạng Thái</th>
                                    <th>Tiến Độ</th>
                                    <th>Hành Động</th>
                                </tr>
                            </thead>
                            <tbody id="tasks-tbody">
                                <!-- Tasks will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Accounts Page -->
            <div id="accounts-page" class="page-content">
                <div class="accounts-section">
                    <div class="section-header">
                        <h2>Tài Khoản Facebook</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" id="bulk-add-accounts">
                                <i class="fas fa-plus-circle"></i>
                                Thêm Hàng Loạt
                            </button>
                            <button class="btn btn-secondary" id="check-all-accounts">
                                <i class="fas fa-check-circle"></i>
                                Kiểm Tra Tất Cả
                            </button>
                            <button class="btn btn-success" id="login-all-accounts">
                                <i class="fas fa-rocket"></i>
                                Login Tất Cả
                            </button>
                            <button class="btn btn-info" id="arrange-browsers">
                                <i class="fas fa-th"></i>
                                Sắp Xếp Browsers
                            </button>
                            <button class="btn btn-warning" id="auto-login-all">
                                <i class="fas fa-magic"></i>
                                Auto Login All
                            </button>
                            <button class="btn btn-secondary" id="auto-login-stop" style="display:none;">
                                <i class="fas fa-stop"></i>
                                Stop Auto Login
                            </button>
                            <button class="btn btn-danger" id="clear-all-accounts">
                                <i class="fas fa-trash-alt"></i>
                                Xóa Tất Cả
                            </button>
                        </div>
                    </div>
                    <div class="accounts-grid" id="accounts-grid">
                        <!-- Accounts will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Proxies Page -->
            <div id="proxies-page" class="page-content">
                <div class="proxies-section">
                    <div class="section-header">
                        <h2>Quản Lý Proxy</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" id="bulk-add-proxies">
                                <i class="fas fa-plus-circle"></i>
                                Thêm Proxy Hàng Loạt
                            </button>
                            <button class="btn btn-secondary" id="test-all-proxies">
                                <i class="fas fa-check-circle"></i>
                                Test Tất Cả Proxy
                            </button>
                            <button class="btn btn-secondary" id="import-proxy-file">
                                <i class="fas fa-file-import"></i>
                                Import File
                            </button>
                            <button class="btn btn-danger" id="clear-all-proxies">
                                <i class="fas fa-trash-alt"></i>
                                Xóa Tất Cả Proxy
                            </button>
                        </div>
                    </div>

                    <div class="proxy-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-proxies">0</h3>
                                <p>Tổng Proxy</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="working-proxies">0</h3>
                                <p>Hoạt Động</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="failed-proxies">0</h3>
                                <p>Lỗi</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="proxy-countries">0</h3>
                                <p>Quốc Gia</p>
                            </div>
                        </div>
                    </div>

                    <div class="proxies-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>IP:Port</th>
                                    <th>Quốc Gia</th>
                                    <th>Loại</th>
                                    <th>Trạng Thái</th>
                                    <th>Tốc Độ</th>
                                    <th>Lần Test Cuối</th>
                                    <th>Hành Động</th>
                                </tr>
                            </thead>
                            <tbody id="proxies-tbody">
                                <!-- Proxies will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Logs Page -->
            <div id="logs-page" class="page-content">
                <div class="logs-section">
                    <div class="section-header">
                        <h2>Nhật Ký Hệ Thống</h2>
                        <div class="section-actions">
                            <select id="log-level-filter" class="form-select">
                                <option value="all">Tất Cả Mức Độ</option>
                                <option value="info">Thông Tin</option>
                                <option value="success">Thành Công</option>
                                <option value="warn">Cảnh Báo</option>
                                <option value="error">Lỗi</option>
                            </select>
                            <button class="btn btn-secondary" id="refresh-logs">
                                <i class="fas fa-sync"></i>
                                Làm Mới
                            </button>
                            <button class="btn btn-secondary" id="clear-logs">
                                <i class="fas fa-trash"></i>
                                Xóa Nhật Ký
                            </button>
                            <button class="btn btn-secondary" id="export-logs">
                                <i class="fas fa-download"></i>
                                Xuất File
                            </button>
                        </div>
                    </div>
                    <div class="logs-container" id="logs-container">
                        <!-- Logs will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page-content">
                <div class="settings-section">
                    <div class="section-header">
                        <h2>Cài Đặt</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" id="save-settings">
                                <i class="fas fa-save"></i>
                                Lưu Cài Đặt
                            </button>
                            <button class="btn btn-secondary" id="reset-settings">
                                <i class="fas fa-undo"></i>
                                Khôi Phục Mặc Định
                            </button>
                        </div>
                    </div>

                    <div class="settings-grid">
                        <!-- Automation Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <i class="fas fa-robot"></i>
                                <h3>Cài Đặt Tự Động Hóa</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>Độ Trễ Giữa Các Hành Động (ms):</label>
                                    <input type="number" id="delay-between-actions" value="3000" min="1000" max="10000">
                                    <small>Khuyến nghị: 3000-5000ms để tránh bị phát hiện</small>
                                </div>
                                <div class="form-group">
                                    <label>Số Hành Động Tối Đa Mỗi Phiên:</label>
                                    <input type="number" id="max-actions-session" value="50" min="10" max="200">
                                    <small>Số hành động tối đa trước khi nghỉ</small>
                                </div>
                                <div class="form-group">
                                    <label>Thời Gian Nghỉ Giữa Phiên (phút):</label>
                                    <input type="number" id="session-break" value="15" min="5" max="60">
                                    <small>Thời gian nghỉ giữa các phiên tự động hóa</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="randomize-delays">
                                        Ngẫu Nhiên Hóa Độ Trễ
                                    </label>
                                    <small>Thêm biến thiên ngẫu nhiên vào độ trễ (khuyến nghị)</small>
                                </div>
                            </div>
                        </div>

                        <!-- Browser Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <i class="fas fa-browser"></i>
                                <h3>Cài Đặt Trình Duyệt</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="headless-mode">
                                        Chế Độ Ẩn
                                    </label>
                                    <small>Chạy trình duyệt ở chế độ nền (nhanh hơn nhưng không hiển thị)</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="disable-images" checked>
                                        Tắt Hình Ảnh
                                    </label>
                                    <small>Tải trang nhanh hơn và tiết kiệm băng thông</small>
                                </div>
                                <div class="form-group">
                                    <label>Số Trình Duyệt Đồng Thời Tối Đa:</label>
                                    <input type="number" id="max-browsers" value="5" min="1" max="20">
                                    <small>Số lượng trình duyệt chạy cùng lúc</small>
                                </div>
                                <div class="form-group">
                                    <label>Thời Gian Chờ Trình Duyệt (giây):</label>
                                    <input type="number" id="browser-timeout" value="30" min="10" max="120">
                                    <small>Thời gian chờ tải trang và thực hiện hành động</small>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <i class="fas fa-shield-alt"></i>
                                <h3>Cài Đặt Bảo Mật</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="auto-proxy-rotation" checked>
                                        Tự Động Xoay Proxy
                                    </label>
                                    <small>Tự động xoay proxy cho mỗi tài khoản</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="fingerprint-spoofing" checked>
                                        Giả Mạo Dấu Vân Tay
                                    </label>
                                    <small>Ngẫu nhiên hóa dấu vân tay trình duyệt</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="clear-cookies">
                                        Xóa Cookies Khi Khởi Động Lại
                                    </label>
                                    <small>Xóa cookies khi khởi động lại tự động hóa</small>
                                </div>
                                <div class="form-group">
                                    <label>Xoay User Agent:</label>
                                    <select id="user-agent-rotation">
                                        <option value="random">Ngẫu Nhiên</option>
                                        <option value="chrome">Chỉ Chrome</option>
                                        <option value="firefox">Chỉ Firefox</option>
                                        <option value="fixed">Cố Định</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Comment Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <i class="fas fa-comment"></i>
                                <h3>Cài Đặt Comment</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>Mẫu Comment:</label>
                                    <textarea id="comment-templates" rows="6" placeholder="Nhập comment, mỗi dòng một comment">Bài viết hay quá! 👍
Cảm ơn bạn đã chia sẻ!
Thú vị quá! 🤔
Yêu thích! ❤️
Nội dung tuyệt vời!
Làm tốt lắm! 🔥</textarea>
                                    <small>Mỗi dòng một comment. Hỗ trợ emoji.</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="random-comments" checked>
                                        Sử Dụng Comment Ngẫu Nhiên
                                    </label>
                                    <small>Chọn ngẫu nhiên từ danh sách mẫu comment</small>
                                </div>
                            </div>
                        </div>

                        <!-- Notification Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <i class="fas fa-bell"></i>
                                <h3>Thông Báo</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="desktop-notifications" checked>
                                        Thông Báo Desktop
                                    </label>
                                    <small>Hiển thị thông báo desktop cho các sự kiện quan trọng</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="sound-notifications">
                                        Thông Báo Âm Thanh
                                    </label>
                                    <small>Phát âm thanh khi có thông báo</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="email-notifications">
                                        Thông Báo Email
                                    </label>
                                    <small>Gửi email cảnh báo khi có lỗi và hoàn thành</small>
                                </div>
                                <div class="form-group">
                                    <label>Địa Chỉ Email:</label>
                                    <input type="email" id="notification-email" placeholder="<EMAIL>">
                                    <small>Địa chỉ email để nhận thông báo</small>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <i class="fas fa-cogs"></i>
                                <h3>Cài Đặt Nâng Cao</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>Mức Độ Log:</label>
                                    <select id="log-level">
                                        <option value="info">Thông Tin</option>
                                        <option value="warn">Cảnh Báo</option>
                                        <option value="error">Chỉ Lỗi</option>
                                        <option value="debug">Debug (Phát Triển)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="auto-backup" checked>
                                        Tự Động Sao Lưu Dữ Liệu
                                    </label>
                                    <small>Tự động sao lưu tài khoản và cài đặt hàng ngày</small>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="auto-update-check" checked>
                                        Kiểm Tra Cập Nhật
                                    </label>
                                    <small>Tự động kiểm tra cập nhật FB NEXUS</small>
                                </div>
                                <div class="form-group">
                                    <label>Giới Hạn API (yêu cầu/phút):</label>
                                    <input type="number" id="api-rate-limit" value="60" min="10" max="300">
                                    <small>Giới hạn yêu cầu API để tránh quá tải</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="add-account-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Thêm Tài Khoản Facebook</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-group">
                        <label>Email/Số Điện Thoại:</label>
                        <input type="text" id="account-email" required>
                    </div>
                    <div class="form-group">
                        <label>Mật Khẩu:</label>
                        <input type="password" id="account-password" required>
                    </div>
                    <div class="form-group">
                        <label>Mã 2FA (tùy chọn):</label>
                        <input type="text" id="account-2fa">
                    </div>
                    <div class="form-group">
                        <label>Proxy (tùy chọn):</label>
                        <select id="account-proxy-select">
                            <option value="">Không Proxy</option>
                            <!-- Proxies will be populated here -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancel-add-account">Hủy</button>
                <button class="btn btn-primary" id="save-account">Thêm Tài Khoản</button>
            </div>
        </div>
    </div>

    <!-- Bulk Add Proxies Modal -->
    <div id="bulk-add-proxies-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Thêm Proxy Hàng Loạt</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Dán proxy theo định dạng: <code>ip:port:user:pass</code> hoặc <code>ip:port</code> (mỗi dòng một proxy)</p>
                <textarea id="bulk-proxies-text" rows="15" placeholder="Ví dụ:
***********:8080:username:password
***********:8080
proxy.example.com:3128:user:pass"></textarea>
                <div class="form-group">
                    <label>Loại Proxy:</label>
                    <select id="proxy-type">
                        <option value="http">HTTP</option>
                        <option value="https">HTTPS</option>
                        <option value="socks4">SOCKS4</option>
                        <option value="socks5">SOCKS5</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="app.hideBulkProxiesModal()">Hủy</button>
                <button class="btn btn-primary" onclick="app.processBulkProxies()">Thêm Proxy</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
