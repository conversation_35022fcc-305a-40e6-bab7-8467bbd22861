// FB NEXUS - Account Manager
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const Logger = require('../utils/Logger');
const Database = require('../database');

class AccountManager {
    constructor() {
        this.logger = new Logger();
        this.database = new Database();
        this.accounts = [];
        this.init();
    }

    async init() {
        try {
            // Load existing accounts from database
            await this.loadAccounts();

            this.logger.info('AccountManager initialized', { accountCount: this.accounts.length });
        } catch (error) {
            this.logger.error('AccountManager init error:', error);
        }
    }

    async loadAccounts() {
        try {
            this.accounts = await this.database.getAccounts();
            this.logger.info(`💾 Loaded ${this.accounts.length} accounts from database`);

            // Debug: Check if accounts have IDs
            for (const acc of this.accounts) {
                if (!acc.id) {
                    this.logger.warn(`⚠️ Account missing ID: ${acc.email || 'unknown'}`);
                } else {
                    this.logger.info(`✅ Account loaded: ${acc.email} (ID: ${acc.id})`);
                }
            }
        } catch (error) {
            this.logger.error('❌ Error loading accounts:', error);
            this.accounts = [];
        }
    }

    async saveAccounts() {
        try {
            await this.database.saveAccounts(this.accounts);
            this.logger.info(`💾 Saved ${this.accounts.length} accounts to database`);
        } catch (error) {
            this.logger.error('❌ Error saving accounts:', error);
        }
    }

    async addAccount(accountData) {
        try {
            const { email, password, twoFA, proxy, proxyId } = accountData;

            // Check if account already exists
            const existingAccount = this.accounts.find(acc => acc.email === email);
            if (existingAccount) {
                throw new Error('Account already exists');
            }

            // Auto-assign US proxy if not provided
            let assignedProxyId = proxyId;
            if (!proxyId && !proxy) {
                const proxyResult = await this.autoAssignUSProxy();
                if (!proxyResult.success) {
                    throw new Error(proxyResult.error);
                }
                assignedProxyId = proxyResult.proxyId;
                this.logger.info(`🇺🇸 Auto-assigned US proxy: ${proxyResult.proxyInfo}`);
            }

            // Create new account
            const account = {
                id: uuidv4(),
                email,
                password,
                twoFA: twoFA || '',
                proxy: proxy || '',
                proxyId: assignedProxyId || '',
                name: email.split('@')[0],
                status: 'inactive',
                createdAt: new Date().toISOString(),
                lastChecked: null,
                lastActive: null,
                loginAttempts: 0,
                isBlocked: false,
                notes: ''
            };

            // Thêm vào database
            const savedAccount = await this.database.addAccount(account);
            if (savedAccount) {
                // Reload accounts from database to ensure sync
                await this.loadAccounts();
                this.logger.info('✅ Account added to database:', { id: account.id, email: account.email });

                // Return account without sensitive data for frontend
                return this.sanitizeAccount(savedAccount);
            } else {
                throw new Error('Failed to save account to database');
            }

        } catch (error) {
            this.logger.error('Add account error:', error);
            throw error;
        }
    }

    /**
     * Auto-assign US proxy to account
     */
    async autoAssignUSProxy() {
        try {
            // Get all proxies
            const proxies = await this.database.getProxies();
            if (!proxies || proxies.length === 0) {
                return {
                    success: false,
                    error: 'No proxies available. Please add proxies first.'
                };
            }

            // Filter US proxies only with comprehensive detection
            const usProxies = proxies.filter(proxy => {
                const country = (proxy.country || '').toLowerCase();
                const location = (proxy.location || '').toLowerCase();
                const notes = (proxy.notes || '').toLowerCase();
                const ip = proxy.ip || '';

                // Check country/location fields
                const isUSByLocation = country.includes('us') ||
                                     country.includes('usa') ||
                                     country.includes('united states') ||
                                     country.includes('america') ||
                                     country.includes('american') ||
                                     location.includes('us') ||
                                     location.includes('usa') ||
                                     location.includes('united states') ||
                                     location.includes('america') ||
                                     notes.includes('us') ||
                                     notes.includes('usa') ||
                                     notes.includes('america');

                // Check US IP ranges (common US datacenter ranges)
                const isUSByIP = ip.startsWith('192.') ||  // Private/test ranges
                               ip.startsWith('10.') ||
                               ip.startsWith('172.') ||
                               ip.startsWith('8.8.') ||    // Google DNS
                               ip.startsWith('1.1.') ||    // Cloudflare
                               ip.startsWith('4.') ||      // Level3
                               ip.startsWith('23.') ||     // Akamai
                               ip.startsWith('104.') ||    // Cloudflare
                               ip.startsWith('173.') ||    // Various US ISPs
                               ip.startsWith('199.') ||    // Various US ISPs
                               ip.startsWith('208.') ||    // Various US ISPs
                               ip.startsWith('209.') ||    // Various US ISPs
                               ip.startsWith('216.');      // Various US ISPs

                return isUSByLocation || isUSByIP;
            });

            // Log proxy classification stats
            const nonUSProxies = proxies.length - usProxies.length;
            this.logger.info(`📊 Proxy Classification: ${usProxies.length} US proxies, ${nonUSProxies} non-US proxies (Total: ${proxies.length})`);

            if (usProxies.length === 0) {
                this.logger.error(`❌ No US proxies found in ${proxies.length} total proxies`);
                return {
                    success: false,
                    error: `No US proxies available. Found ${proxies.length} total proxies but none are US-based. Please add US proxies first.`
                };
            }

            // Get all accounts to check proxy usage
            const accounts = await this.database.getAccounts();
            const usedProxyIds = accounts
                .filter(acc => acc.proxyId)
                .map(acc => acc.proxyId);

            // Analyze US proxy usage
            const usedUSProxies = usProxies.filter(proxy => usedProxyIds.includes(proxy.id));
            const availableUSProxies = usProxies.filter(proxy => !usedProxyIds.includes(proxy.id));

            this.logger.info(`📈 US Proxy Usage: ${availableUSProxies.length} available, ${usedUSProxies.length} already used (Total US: ${usProxies.length})`);

            // Find unused US proxy first
            if (availableUSProxies.length > 0) {
                const selectedProxy = availableUSProxies[0]; // Take first available

                this.logger.info(`✅ Assigning unused US proxy: ${selectedProxy.ip}:${selectedProxy.port}`);
                return {
                    success: true,
                    proxyId: selectedProxy.id,
                    proxyInfo: `${selectedProxy.ip}:${selectedProxy.port} (US - Fresh)`,
                    isDuplicate: false,
                    stats: {
                        totalProxies: proxies.length,
                        usProxies: usProxies.length,
                        availableUS: availableUSProxies.length,
                        usedUS: usedUSProxies.length
                    }
                };
            }

            // All US proxies are used - use round-robin
            this.logger.warn(`⚠️ DUPLICATE PROXY WARNING: All ${usProxies.length} US proxies are already assigned!`);

            // Use round-robin assignment for US proxies
            const proxyIndex = accounts.length % usProxies.length;
            const selectedProxy = usProxies[proxyIndex];

            // Find which accounts are using this proxy
            const accountsUsingProxy = accounts.filter(acc => acc.proxyId === selectedProxy.id);
            this.logger.warn(`🔄 Round-robin assignment: ${selectedProxy.ip}:${selectedProxy.port} (Already used by ${accountsUsingProxy.length} accounts)`);

            return {
                success: true,
                proxyId: selectedProxy.id,
                proxyInfo: `${selectedProxy.ip}:${selectedProxy.port} (US - Duplicate #${accountsUsingProxy.length + 1})`,
                isDuplicate: true,
                duplicateCount: accountsUsingProxy.length + 1,
                stats: {
                    totalProxies: proxies.length,
                    usProxies: usProxies.length,
                    availableUS: 0,
                    usedUS: usProxies.length
                }
            };

        } catch (error) {
            this.logger.error('❌ Error auto-assigning US proxy:', error);
            return {
                success: false,
                error: `Failed to assign proxy: ${error.message}`
            };
        }
    }

    async bulkAddAccounts(accountsData) {
        try {
            const results = [];
            const errors = [];

            for (const accountData of accountsData) {
                try {
                    const account = await this.addAccount(accountData);
                    results.push(account);
                } catch (error) {
                    errors.push({
                        email: accountData.email,
                        error: error.message
                    });
                }
            }

            // Reload accounts from database to ensure sync
            await this.loadAccounts();

            this.logger.info('Bulk add accounts completed:', {
                added: results.length,
                errors: errors.length
            });

            return results;

        } catch (error) {
            this.logger.error('Bulk add accounts error:', error);
            throw error;
        }
    }

    async clearAllAccounts() {
        try {
            this.logger.info('🗑️ Starting to clear all accounts...');
            this.logger.info('📊 Current accounts count:', { count: this.accounts.length });

            // Clear from database
            this.logger.info('💾 Clearing accounts from database...');
            const result = await this.database.clearAllAccounts();

            if (result) {
                // Clear from memory
                this.accounts = [];
                this.logger.info('📝 Accounts cleared from memory');

                this.logger.info('✅ All accounts cleared from database and memory successfully');
                return true;
            } else {
                this.logger.error('❌ Failed to clear accounts from database');
                return false;
            }

        } catch (error) {
            this.logger.error('❌ Clear all accounts error:', { error: error.message, stack: error.stack });
            throw error;
        }
    }

    async removeAccount(accountId) {
        try {
            this.logger.info('🗑️ Starting to remove account:', { id: accountId });

            const accountIndex = this.accounts.findIndex(acc => acc.id === accountId);
            if (accountIndex === -1) {
                this.logger.error('❌ Account not found in memory:', { id: accountId });
                throw new Error(`Account not found: ${accountId}`);
            }

            const removedAccount = this.accounts.splice(accountIndex, 1)[0];
            this.logger.info('📝 Account removed from memory:', { id: accountId, email: removedAccount.email });

            // Xóa khỏi database
            this.logger.info('💾 Deleting account from database:', { id: accountId });
            const deleted = await this.database.deleteAccount(accountId);

            if (deleted) {
                this.logger.info('✅ Account deleted from database:', { id: accountId });

                // Clean up profile directory
                const profileDir = path.join(__dirname, '../../profiles', accountId);
                if (await fs.pathExists(profileDir)) {
                    await fs.remove(profileDir);
                    this.logger.info('🗂️ Profile directory cleaned:', { dir: profileDir });
                }

                // Reload accounts from database to ensure sync
                await this.loadAccounts();
                this.logger.info('✅ Account removed successfully:', { id: accountId, email: removedAccount.email });
            } else {
                this.logger.error('❌ Failed to delete account from database:', { id: accountId });
                throw new Error(`Failed to delete account from database: ${accountId}`);
            }

        } catch (error) {
            this.logger.error('❌ Remove account error:', { id: accountId, error: error.message, stack: error.stack });
            throw error;
        }
    }

    async updateAccount(accountId, updates) {
        try {
            const account = this.accounts.find(acc => acc.id === accountId);
            if (!account) {
                throw new Error('Account not found');
            }

            // Update allowed fields
            const allowedFields = ['name', 'proxy', 'twoFA', 'status', 'notes'];
            for (const field of allowedFields) {
                if (updates.hasOwnProperty(field)) {
                    account[field] = updates[field];
                }
            }

            account.updatedAt = new Date().toISOString();

            // Cập nhật trong database
            const updatedAccount = await this.database.updateAccount(accountId, account);
            if (updatedAccount) {
                this.logger.info('✅ Account updated in database:', { id: accountId, updates: Object.keys(updates) });
                return this.sanitizeAccount(updatedAccount);
            } else {
                throw new Error('Failed to update account in database');
            }

        } catch (error) {
            this.logger.error('Update account error:', error);
            throw error;
        }
    }

    async checkAccount(accountId) {
        try {
            const account = this.accounts.find(acc => acc.id === accountId);
            if (!account) {
                throw new Error('Account not found');
            }

            // Update last checked time
            account.lastChecked = new Date().toISOString();
            
            // Here you would implement actual account checking logic
            // For now, we'll simulate a check
            const isActive = Math.random() > 0.3; // 70% chance of being active
            account.status = isActive ? 'active' : 'inactive';
            
            if (isActive) {
                account.lastActive = new Date().toISOString();
                account.loginAttempts = 0;
            } else {
                account.loginAttempts += 1;
                if (account.loginAttempts >= 3) {
                    account.isBlocked = true;
                    account.status = 'blocked';
                }
            }

            await this.saveAccounts();

            this.logger.info('Account checked:', { 
                id: accountId, 
                status: account.status,
                loginAttempts: account.loginAttempts 
            });

            return {
                id: accountId,
                status: account.status,
                isBlocked: account.isBlocked,
                lastChecked: account.lastChecked,
                lastActive: account.lastActive,
                loginAttempts: account.loginAttempts
            };

        } catch (error) {
            this.logger.error('Check account error:', error);
            throw error;
        }
    }

    async getAllAccounts() {
        return this.accounts.map(account => this.sanitizeAccount(account));
    }

    async getAccount(accountId) {
        const account = this.accounts.find(acc => acc.id === accountId);
        return account ? this.sanitizeAccount(account) : null;
    }

    async getAccountWithCredentials(accountId) {
        // This method returns account with credentials for automation use
        const account = this.accounts.find(acc => acc.id === accountId);
        return account || null;
    }

    async validateAccounts(accountIds) {
        const validAccounts = [];
        
        for (const accountId of accountIds) {
            const account = this.accounts.find(acc => acc.id === accountId);
            if (account && !account.isBlocked && account.status !== 'blocked') {
                validAccounts.push(account);
            }
        }

        return validAccounts;
    }

    async getActiveAccounts() {
        return this.accounts
            .filter(acc => acc.status === 'active' && !acc.isBlocked)
            .map(account => this.sanitizeAccount(account));
    }

    async getAccountStats() {
        const total = this.accounts.length;
        const active = this.accounts.filter(acc => acc.status === 'active').length;
        const inactive = this.accounts.filter(acc => acc.status === 'inactive').length;
        const blocked = this.accounts.filter(acc => acc.isBlocked).length;

        return {
            total,
            active,
            inactive,
            blocked,
            activePercentage: total > 0 ? Math.round((active / total) * 100) : 0
        };
    }

    async importAccountsFromFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const lines = content.split('\n').filter(line => line.trim());
            
            const accounts = [];
            for (const line of lines) {
                const parts = line.split('|');
                if (parts.length >= 2) {
                    accounts.push({
                        email: parts[0].trim(),
                        password: parts[1].trim(),
                        twoFA: parts[2] ? parts[2].trim() : '',
                        proxy: parts[3] ? parts[3].trim() : ''
                    });
                }
            }

            const results = await this.bulkAddAccounts(accounts);
            
            this.logger.info('Accounts imported from file:', { 
                file: filePath, 
                imported: results.length 
            });

            return results;

        } catch (error) {
            this.logger.error('Import accounts error:', error);
            throw error;
        }
    }

    async exportAccountsToFile(filePath, includeCredentials = false) {
        try {
            let content = '';
            
            for (const account of this.accounts) {
                if (includeCredentials) {
                    content += `${account.email}|${account.password}|${account.twoFA}|${account.proxy}\n`;
                } else {
                    content += `${account.email}|${account.status}|${account.lastActive || 'Never'}\n`;
                }
            }

            await fs.writeFile(filePath, content);
            
            this.logger.info('Accounts exported to file:', { 
                file: filePath, 
                count: this.accounts.length 
            });

        } catch (error) {
            this.logger.error('Export accounts error:', error);
            throw error;
        }
    }

    /**
     * Update account status and login info
     */
    async updateAccountStatus(id, statusData) {
        try {
            const accountIndex = this.accounts.findIndex(acc => acc.id === id);
            if (accountIndex === -1) {
                this.logger.warn('Account not found for status update:', { id });
                return false;
            }

            const account = this.accounts[accountIndex];

            // Update account in memory
            if (statusData.status) {
                account.status = statusData.status;
            }

            if (statusData.lastLogin) {
                account.lastLogin = statusData.lastLogin;
            }

            if (statusData.lastLoginAttempt) {
                account.lastLoginAttempt = statusData.lastLoginAttempt;
            }

            if (statusData.loginUrl) {
                account.loginUrl = statusData.loginUrl;
            }

            if (statusData.isActive !== undefined) {
                account.isActive = statusData.isActive;
            }

            account.updatedAt = new Date().toISOString();

            // Update in database
            await this.database.updateAccount(id, {
                status: account.status,
                lastLogin: account.lastLogin,
                lastLoginAttempt: account.lastLoginAttempt,
                loginUrl: account.loginUrl,
                isActive: account.isActive,
                updatedAt: account.updatedAt
            });

            this.logger.info('✅ Account status updated:', {
                id,
                email: account.email,
                status: account.status,
                isActive: account.isActive
            });

            return true;

        } catch (error) {
            this.logger.error('❌ Error updating account status:', error);
            return false;
        }
    }

    sanitizeAccount(account) {
        // Return account without sensitive data
        const { password, ...sanitized } = account;
        return sanitized;
    }

    async cleanup() {
        this.logger.info('AccountManager cleanup');
        // Perform any necessary cleanup
    }
}

module.exports = AccountManager;
