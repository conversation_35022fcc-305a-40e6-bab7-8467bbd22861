const Logger = require('../utils/Logger');
const ChromeManager = require('./ChromeManager');
const AccountManager = require('./AccountManager');

class AutoLoginManager {
    constructor() {
        this.logger = new Logger('AutoLoginManager');
        this.chromeManager = new ChromeManager();
        this.accountManager = new AccountManager();
        this.isRunning = false;
        this.loginQueue = [];
        this.successCount = 0;
        this.failCount = 0;
        this.captchaCount = 0;
        this.currentBatch = [];
        this.maxConcurrent = 1; // Login 1 account at a time to avoid captcha
        this.delayBetweenLogins = 10000; // 10 seconds delay
        this.captchaAccounts = new Set(); // Track accounts with captcha
    }

    /**
     * Start auto login process for all accounts
     */
    async startAutoLogin() {
        if (this.isRunning) {
            this.logger.warn('⚠️ Auto login already running!');
            return { success: false, message: 'Auto login already in progress' };
        }

        try {
            this.logger.info('🚀 Starting FULLY AUTOMATED LOGIN SYSTEM...');
            this.isRunning = true;
            this.successCount = 0;
            this.failCount = 0;
            this.captchaCount = 0;
            this.captchaAccounts.clear();

            // Get all accounts that need login (with credentials for automation)
            const allAccounts = await this.accountManager.getAllAccounts();
            const loginNeeded = [];

            for (const acc of allAccounts) {
                if (acc.email && acc.email.trim() !== '' && acc.status !== 'logged_in') {
                    // Get account with credentials for automation
                    const accountWithCreds = await this.accountManager.getAccountWithCredentials(acc.id);
                    if (accountWithCreds) {
                        this.logger.info(`✅ Account loaded for auto-login: ${accountWithCreds.email} (ID: ${accountWithCreds.id})`);
                        loginNeeded.push(accountWithCreds);
                    } else {
                        this.logger.warn(`⚠️ Could not load credentials for account: ${acc.email} (ID: ${acc.id})`);
                    }
                }
            }

            if (loginNeeded.length === 0) {
                this.logger.info('✅ All accounts already logged in!');
                this.isRunning = false;
                return { success: true, message: 'All accounts already logged in' };
            }

            this.logger.info(`📋 Found ${loginNeeded.length} accounts to login`);
            this.loginQueue = [...loginNeeded];

            // Start the login process
            await this.processLoginQueue();

            this.isRunning = false;
            
            const summary = {
                total: loginNeeded.length,
                success: this.successCount,
                failed: this.failCount,
                captcha: this.captchaCount,
                captchaAccounts: Array.from(this.captchaAccounts)
            };

            this.logger.info('🎉 AUTO LOGIN COMPLETED!');
            this.logger.info(`📊 SUMMARY: ✅ ${this.successCount} success, ❌ ${this.failCount} failed, 🤖 ${this.captchaCount} captcha`);

            return { success: true, summary };

        } catch (error) {
            this.logger.error('❌ Auto login error:', error);
            this.isRunning = false;
            return { success: false, error: error.message };
        }
    }

    /**
     * Process login queue one by one
     */
    async processLoginQueue() {
        while (this.loginQueue.length > 0 && this.isRunning) {
            const account = this.loginQueue.shift();
            
            this.logger.info(`🔐 Processing login for: ${account.email} (${this.loginQueue.length} remaining)`);

            try {
                // Launch browser first
                this.logger.info(`🌐 Launching browser for: ${account.email} (ID: ${account.id})`);
                const launchResult = await this.chromeManager.launchBrowser(account.id);
                
                if (!launchResult.success) {
                    this.logger.error(`❌ Failed to launch browser for: ${account.email}`);
                    this.failCount++;
                    continue;
                }

                // Wait a bit for browser to stabilize
                await this.delay(3000);

                // Attempt login
                this.logger.info(`🔑 Attempting login for: ${account.email}`);
                const loginResult = await this.chromeManager.loginFacebook(account.id);

                if (loginResult.success) {
                    this.logger.info(`✅ LOGIN SUCCESS: ${account.email}`);
                    this.successCount++;
                    
                    // Update account status
                    await this.accountManager.updateAccount(account.id, {
                        status: 'logged_in',
                        isActive: true,
                        lastLogin: new Date().toISOString()
                    });

                    this.logger.info(`🔄 Enabling US proxy for: ${account.email}`);
                    // Proxy should be auto-enabled by VN IP First Strategy

                } else if (loginResult.status === 'captcha_required') {
                    this.logger.warn(`🤖 CAPTCHA DETECTED for: ${account.email} - Skipping for now`);
                    this.captchaCount++;
                    this.captchaAccounts.add(account.email);
                    
                    // Close browser to save resources
                    await this.chromeManager.closeBrowser(account.id);
                    
                } else {
                    this.logger.error(`❌ LOGIN FAILED: ${account.email} - ${loginResult.message}`);
                    this.failCount++;
                    
                    // Close browser
                    await this.chromeManager.closeBrowser(account.id);
                }

            } catch (error) {
                this.logger.error(`❌ Error processing ${account.email}:`, error);
                this.failCount++;
                
                // Close browser on error
                try {
                    await this.chromeManager.closeBrowser(account.id);
                } catch (closeError) {
                    // Ignore close errors
                }
            }

            // Delay between logins to avoid detection
            if (this.loginQueue.length > 0) {
                this.logger.info(`⏳ Waiting ${this.delayBetweenLogins/1000}s before next login...`);
                await this.delay(this.delayBetweenLogins);
            }
        }
    }

    /**
     * Stop auto login process
     */
    async stopAutoLogin() {
        this.logger.info('🛑 Stopping auto login process...');
        this.isRunning = false;
        this.loginQueue = [];
        return { success: true, message: 'Auto login stopped' };
    }

    /**
     * Get current status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            queueLength: this.loginQueue.length,
            successCount: this.successCount,
            failCount: this.failCount,
            captchaCount: this.captchaCount,
            captchaAccounts: Array.from(this.captchaAccounts),
            currentAccount: this.loginQueue[0]?.email || null
        };
    }

    /**
     * Retry captcha accounts manually
     */
    async retryCaptchaAccounts() {
        if (this.captchaAccounts.size === 0) {
            return { success: true, message: 'No captcha accounts to retry' };
        }

        this.logger.info(`🔄 Retrying ${this.captchaAccounts.size} captcha accounts...`);
        
        const accounts = await this.accountManager.getAllAccounts();
        const captchaAccountsToRetry = accounts.filter(acc => 
            this.captchaAccounts.has(acc.email)
        );

        this.loginQueue = [...captchaAccountsToRetry];
        this.captchaAccounts.clear();
        this.captchaCount = 0;

        if (!this.isRunning) {
            await this.processLoginQueue();
        }

        return { success: true, message: `Retrying ${captchaAccountsToRetry.length} accounts` };
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = AutoLoginManager;
