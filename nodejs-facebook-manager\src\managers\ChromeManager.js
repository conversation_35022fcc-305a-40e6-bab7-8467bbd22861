/**
 * Chrome Manager - <PERSON><PERSON><PERSON>n lý Chrome instances với fingerprint spoofing
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const AnonymizeUAPlugin = require('puppeteer-extra-plugin-anonymize-ua');
const path = require('path');
const fs = require('fs-extra');
const geoip = require('geoip-lite');
const moment = require('moment-timezone');
const Logger = require('../utils/Logger');

// Add plugins
puppeteer.use(StealthPlugin());
puppeteer.use(AnonymizeUAPlugin());

class ChromeManager {
    constructor() {
        this.instances = new Map(); // accountId -> browser instance
        this.logger = new Logger('ChromeManager');
        this.profilesDir = path.join(__dirname, '../../profiles');
        this.settings = null; // Cache settings

        // Fix memory leaks - increase max listeners
        process.setMaxListeners(50);

        // Ensure profiles directory exists
        fs.ensureDirSync(this.profilesDir);
    }

    /**
     * Load settings from database
     */
    async loadSettings() {
        try {
            if (!this.settings) {
                const Database = require('../database');
                const db = new Database();
                this.settings = await db.getSettings();
                this.logger.info('📋 Settings loaded:', Object.keys(this.settings));
            }
            return this.settings;
        } catch (error) {
            this.logger.error('❌ Failed to load settings:', error);
            return {};
        }
    }

    /**
     * Launch browser for account with fingerprint and proxy
     */
    async launchBrowser(account) {
        try {
            // Close existing browser if running
            if (this.instances.has(account.id)) {
                await this.closeBrowser(account.id);
            }

            this.logger.info(`🚀 Launching Chrome for account: ${account.email}`);

            // Get proxy for account (if assigned) - MUST BE FIRST
            const proxy = await this.getProxyForAccount(account);

            // Test proxy before launching browser (SKIP FOR NOW - DIRECT LAUNCH)
            if (proxy) {
                this.logger.info(`📡 Proxy configured: ${proxy.ip}:${proxy.port} (${proxy.username ? 'with auth' : 'no auth'})`);
                // Skip proxy test for now - will test after browser launch
                // const proxyTest = await this.testProxyConnection(proxy);
                // if (!proxyTest.success) {
                //     throw new Error(`Proxy test failed: ${proxyTest.error}`);
                // }
                // this.logger.info(`✅ Proxy test passed: ${proxyTest.ip} (${proxyTest.country})`);
            }

            // Generate fingerprint with proxy info
            const fingerprint = this.generateFingerprint(proxy);

            // Build launch options
            const launchOptions = await this.buildLaunchOptions(account, proxy, fingerprint);

            // Launch browser
            const browser = await puppeteer.launch(launchOptions);

            // Store instance
            this.instances.set(account.id, {
                browser,
                fingerprint,
                proxy,
                startTime: new Date(),
                account
            });

            // Setup browser context
            await this.setupBrowserContext(browser, fingerprint);

            // Setup proxy authentication if needed (VN IP FIRST STRATEGY)
            const USE_VN_IP_FIRST_STRATEGY = true;
            if (proxy && this.proxyAuth && this.proxyAuth.username && this.proxyAuth.password && !USE_VN_IP_FIRST_STRATEGY) {
                this.logger.info(`🔐 Setting up proxy authentication for: ${proxy.ip}:${proxy.port}`);
                this.logger.info(`🔑 Using credentials from settings: ${this.proxyAuth.username}:${this.proxyAuth.password}`);
                await this.setupProxyAuth(browser, proxy, this.proxyAuth);

                // Verify proxy is working correctly
                await this.verifyProxyWorking(browser, proxy);
            } else if (USE_VN_IP_FIRST_STRATEGY) {
                this.logger.info('🇻🇳 Using VN IP first strategy - Proxy will be enabled after login');
            } else if (proxy) {
                this.logger.warn(`⚠️ No proxy authentication credentials available`);
                await this.verifyProxyWorking(browser, proxy);
            }

            // ADVANCED ANTI-CAPTCHA PAGE SETUP
            await this.setupAntiCaptchaFeatures(browser);

            // Auto arrange and resize browser windows
            await this.arrangeBrowserWindows();

            this.logger.info(`✅ Chrome launched successfully for: ${account.email}`);

            return {
                success: true,
                fingerprint: this.sanitizeFingerprint(fingerprint),
                proxy: proxy ? `${proxy.ip}:${proxy.port}` : null,
                profilePath: this.getProfilePath(account.id)
            };

        } catch (error) {
            this.logger.error(`❌ Failed to launch Chrome for ${account.email}:`);
            this.logger.error('Launch browser error:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });
            throw error;
        }
    }

    /**
     * Close browser for account
     */
    async closeBrowser(accountId) {
        try {
            const instance = this.instances.get(accountId);
            if (!instance) {
                return { success: true, message: 'Browser not running' };
            }

            this.logger.info(`🔴 Closing Chrome for account: ${accountId}`);

            await instance.browser.close();
            this.instances.delete(accountId);

            this.logger.info(`✅ Chrome closed for account: ${accountId}`);
            
            return { success: true, message: 'Browser closed successfully' };

        } catch (error) {
            this.logger.error(`❌ Failed to close Chrome for ${accountId}:`, error);
            // Remove from instances even if close failed
            this.instances.delete(accountId);
            throw error;
        }
    }

    /**
     * Get browser instance for account
     */
    getBrowser(accountId) {
        const instance = this.instances.get(accountId);
        return instance ? instance.browser : null;
    }

    /**
     * Check if browser is running for account
     */
    isBrowserRunning(accountId) {
        return this.instances.has(accountId);
    }

    /**
     * Get all running browsers
     */
    getRunningBrowsers() {
        const running = [];
        for (const [accountId, instance] of this.instances) {
            running.push({
                accountId,
                email: instance.account.email,
                startTime: instance.startTime,
                proxy: instance.proxy ? `${instance.proxy.ip}:${instance.proxy.port}` : null
            });
        }
        return running;
    }

    /**
     * Build Chrome launch options
     */
    async buildLaunchOptions(account, proxy, fingerprint) {
        const profilePath = this.getProfilePath(account.id);

        // Setup WebRTC Leak Shield extension (only if using proxy)
        const USE_VN_IP_FIRST_STRATEGY = true;
        let webrtcExtensionPath = '';

        if (!USE_VN_IP_FIRST_STRATEGY) {
            webrtcExtensionPath = await this.setupWebRTCExtension();
        }

        const args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            `--user-data-dir=${profilePath}`,
            `--window-size=800,600`, // Start with smaller default size
            '--window-position=0,0', // Start at top-left, will be repositioned
            // Extension args will be added conditionally below
        ];

        // Add extension args only if using proxy
        if (!USE_VN_IP_FIRST_STRATEGY && webrtcExtensionPath) {
            args.push(`--load-extension=${webrtcExtensionPath}`);
            args.push(`--disable-extensions-except=${webrtcExtensionPath}`);
        }

        // Add proxy if provided
        // VN IP FIRST STRATEGY: Skip proxy during initial launch

        if (proxy && !USE_VN_IP_FIRST_STRATEGY) {
            // Load settings to get proxy credentials
            const settings = await this.loadSettings();
            const proxyAuth = {
                username: settings.proxyUsername || proxy.username,
                password: settings.proxyPassword || proxy.password
            };

            // Use proper proxy format for Chrome with embedded credentials
            const proxyType = proxy.type === 'http' ? 'http' : proxy.type;

            // Chrome doesn't support embedded credentials in --proxy-server
            // Use standard format and handle auth via page.authenticate()
            args.push(`--proxy-server=${proxyType}://${proxy.ip}:${proxy.port}`);
            this.logger.info(`📡 Using proxy: ${proxyType}://${proxy.ip}:${proxy.port} (auth via settings)`);

            // Store auth info for later use
            this.proxyAuth = proxyAuth;

            // Force all traffic through proxy
            args.push('--proxy-bypass-list=<-loopback>');
            args.push('--disable-background-networking');
            args.push('--disable-default-apps');
            args.push('--disable-extensions-except=' + webrtcExtensionPath);
            args.push('--disable-sync');
            args.push('--disable-translate');
            args.push('--disable-ipc-flooding-protection');
        } else if (USE_VN_IP_FIRST_STRATEGY) {
            this.logger.info('🇻🇳 VN IP FIRST STRATEGY - Login with Vietnam IP, proxy will be enabled after login');
        }

        // ULTRA ADVANCED ANTI-CAPTCHA & STEALTH ARGS
        args.push('--disable-blink-features=AutomationControlled');
        args.push('--disable-dev-shm-usage');
        args.push('--no-sandbox');
        args.push('--disable-setuid-sandbox');
        args.push('--disable-gpu');
        args.push('--disable-background-timer-throttling');
        args.push('--disable-backgrounding-occluded-windows');
        args.push('--disable-renderer-backgrounding');
        args.push('--disable-features=TranslateUI,VizDisplayCompositor,VizServiceDisplay');
        args.push('--disable-component-extensions-with-background-pages');
        args.push('--disable-component-update');
        args.push('--disable-client-side-phishing-detection');
        args.push('--disable-crash-reporter');
        args.push('--disable-domain-reliability');
        args.push('--disable-field-trial-config');
        args.push('--disable-breakpad');
        args.push('--no-first-run');
        args.push('--no-default-browser-check');
        args.push('--disable-popup-blocking');
        args.push('--disable-prompt-on-repost');
        args.push('--disable-hang-monitor');
        args.push('--disable-web-security');
        args.push('--enable-features=NetworkService,NetworkServiceLogging');
        args.push('--force-color-profile=srgb');
        args.push('--metrics-recording-only');
        args.push('--use-mock-keychain');

        // EXTREME ANTI-CAPTCHA MEASURES
        args.push('--disable-features=VizDisplayCompositor,VizServiceDisplay,AudioServiceOutOfProcess');
        args.push('--disable-ipc-flooding-protection');
        args.push('--disable-renderer-accessibility');
        args.push('--disable-speech-api');
        args.push('--disable-background-media-suspend');
        args.push('--disable-new-content-rendering-timeout');
        args.push('--disable-backgrounding-occluded-windows');
        args.push('--disable-features=TranslateUI,BlinkGenPropertyTrees');
        args.push('--disable-features=site-per-process');
        args.push('--disable-site-isolation-trials');
        args.push('--disable-features=VizDisplayCompositor');
        args.push('--run-all-compositor-stages-before-draw');
        args.push('--disable-threaded-animation');
        args.push('--disable-threaded-scrolling');
        args.push('--disable-checker-imaging');
        args.push('--disable-new-bookmark-apps');
        args.push('--disable-motivation-extension');
        args.push('--disable-search-engine-choice-screen');
        args.push('--disable-dinosaur-easter-egg');

        // Add fingerprint-specific args
        if (fingerprint.userAgent) {
            args.push(`--user-agent=${fingerprint.userAgent}`);
        }

        return {
            headless: false, // Show browser for debugging
            args,
            defaultViewport: fingerprint.viewport,
            userDataDir: profilePath,
            ignoreDefaultArgs: ['--enable-automation', '--enable-blink-features=AutomationControlled'],
            ignoreHTTPSErrors: true,
            // ADVANCED ANTI-DETECTION
            executablePath: undefined, // Use system Chrome
            slowMo: Math.floor(Math.random() * 50) + 25, // Random delay 25-75ms
            devtools: false
        };
    }

    /**
     * Setup advanced browser context with comprehensive fingerprint spoofing
     */
    async setupBrowserContext(browser, fingerprint) {
        try {
            const pages = await browser.pages();
            const page = pages[0] || await browser.newPage();

            // Set user agent
            if (fingerprint.userAgent) {
                await page.setUserAgent(fingerprint.userAgent);
            }

            // Set viewport
            await page.setViewport(fingerprint.viewport);

            // Set extra headers
            await page.setExtraHTTPHeaders(fingerprint.headers);

            // Set timezone
            await page.emulateTimezone(fingerprint.timezone);

            // Set geolocation based on proxy
            if (fingerprint.country) {
                const geoLocation = this.getGeoLocationByCountry(fingerprint.country);
                await page.setGeolocation(geoLocation);
            }

            // Advanced fingerprint spoofing
            await page.evaluateOnNewDocument((fp) => {
                // Override navigator properties
                Object.defineProperty(navigator, 'language', {
                    get: () => fp.language
                });

                Object.defineProperty(navigator, 'languages', {
                    get: () => [fp.language, 'en']
                });

                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32'
                });

                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => fp.hardware.cores
                });

                // Override WebGL fingerprinting with realistic data
                const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
                        return fp.webgl.vendor;
                    }
                    if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
                        return fp.webgl.renderer;
                    }
                    return originalGetParameter.apply(this, arguments);
                };

                // Override canvas fingerprinting with consistent noise
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function(type) {
                    const shift = fp.canvas.noise;
                    const ctx = this.getContext('2d');
                    if (ctx) {
                        const originalImageData = ctx.getImageData(0, 0, this.width, this.height);
                        for (let i = 0; i < originalImageData.data.length; i += 4) {
                            originalImageData.data[i] = Math.min(255, Math.max(0, originalImageData.data[i] + shift));
                        }
                        ctx.putImageData(originalImageData, 0, 0);
                    }
                    return originalToDataURL.apply(this, arguments);
                };

                // Override AudioContext fingerprinting
                const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                AudioContext.prototype.createAnalyser = function() {
                    const analyser = originalCreateAnalyser.apply(this, arguments);
                    const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                    analyser.getFloatFrequencyData = function(array) {
                        originalGetFloatFrequencyData.apply(this, arguments);
                        for (let i = 0; i < array.length; i++) {
                            array[i] += Math.random() * 0.0001;
                        }
                    };
                    return analyser;
                };

                // Override screen properties
                Object.defineProperty(screen, 'width', {
                    get: () => fp.viewport.width
                });

                Object.defineProperty(screen, 'height', {
                    get: () => fp.viewport.height
                });

                Object.defineProperty(screen, 'availWidth', {
                    get: () => fp.viewport.width
                });

                Object.defineProperty(screen, 'availHeight', {
                    get: () => fp.viewport.height - 40 // Account for taskbar
                });

                // Override Date timezone
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                Date.prototype.getTimezoneOffset = function() {
                    // Return offset based on fingerprint timezone
                    return fp.timezoneOffset || originalGetTimezoneOffset.apply(this);
                };

                // Remove automation indicators
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });

                // Override permissions
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(parameters) {
                    return parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery.apply(this, arguments);
                };

            }, fingerprint);

            this.logger.info('✅ Advanced browser context setup completed');

        } catch (error) {
            this.logger.error('❌ Failed to setup browser context:', error);
        }
    }

    /**
     * Get geolocation based on country
     */
    getGeoLocationByCountry(countryCode) {
        const locations = {
            'US': { latitude: 39.8283, longitude: -98.5795 },
            'GB': { latitude: 55.3781, longitude: -3.4360 },
            'DE': { latitude: 51.1657, longitude: 10.4515 },
            'FR': { latitude: 46.2276, longitude: 2.2137 },
            'JP': { latitude: 36.2048, longitude: 138.2529 },
            'CN': { latitude: 35.8617, longitude: 104.1954 },
            'SG': { latitude: 1.3521, longitude: 103.8198 },
            'AU': { latitude: -25.2744, longitude: 133.7751 },
            'CA': { latitude: 56.1304, longitude: -106.3468 },
            'VN': { latitude: 14.0583, longitude: 108.2772 }
        };

        return locations[countryCode] || locations['US'];
    }

    /**
     * Generate advanced fingerprint with proxy-based timezone
     */
    generateFingerprint(proxy = null) {
        // Advanced User Agents (Windows only, latest Chrome versions)
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        ];

        // Realistic screen resolutions
        const viewports = [
            { width: 1920, height: 1080 },
            { width: 1366, height: 768 },
            { width: 1440, height: 900 },
            { width: 1536, height: 864 },
            { width: 1280, height: 720 },
            { width: 1600, height: 900 },
            { width: 1680, height: 1050 },
            { width: 1024, height: 768 }
        ];

        // Get timezone based on proxy location
        let timezone = 'America/New_York'; // Default US timezone
        let country = 'US';

        if (proxy && proxy.ip) {
            const geo = geoip.lookup(proxy.ip);
            if (geo) {
                country = geo.country;
                timezone = this.getTimezoneByCountry(geo.country, geo.city);
            }
        }

        // Generate hardware specs
        const hardwareSpecs = this.generateHardwareSpecs();

        // Generate WebGL specs
        const webglSpecs = this.generateWebGLSpecs();

        return {
            userAgent: userAgents[Math.floor(Math.random() * userAgents.length)],
            viewport: viewports[Math.floor(Math.random() * viewports.length)],
            language: 'en-US', // Always US English as requested
            timezone: timezone,
            country: country,
            hardware: hardwareSpecs,
            webgl: webglSpecs,
            canvas: this.generateCanvasFingerprint(),
            audio: this.generateAudioFingerprint(),
            headers: {
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }
        };
    }

    /**
     * Get timezone based on country code
     */
    getTimezoneByCountry(countryCode, city = null) {
        const timezoneMap = {
            'US': ['America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles'],
            'GB': ['Europe/London'],
            'DE': ['Europe/Berlin'],
            'FR': ['Europe/Paris'],
            'JP': ['Asia/Tokyo'],
            'CN': ['Asia/Shanghai'],
            'KR': ['Asia/Seoul'],
            'SG': ['Asia/Singapore'],
            'AU': ['Australia/Sydney', 'Australia/Melbourne'],
            'CA': ['America/Toronto', 'America/Vancouver'],
            'BR': ['America/Sao_Paulo'],
            'IN': ['Asia/Kolkata'],
            'RU': ['Europe/Moscow'],
            'VN': ['Asia/Ho_Chi_Minh']
        };

        const timezones = timezoneMap[countryCode] || ['America/New_York'];
        return timezones[Math.floor(Math.random() * timezones.length)];
    }

    /**
     * Generate realistic hardware specifications
     */
    generateHardwareSpecs() {
        const cpus = [
            'Intel(R) Core(TM) i7-10700K CPU @ 3.80GHz',
            'Intel(R) Core(TM) i5-9600K CPU @ 3.70GHz',
            'Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz',
            'AMD Ryzen 7 3700X 8-Core Processor',
            'AMD Ryzen 5 3600 6-Core Processor'
        ];

        const rams = [8, 16, 32];
        const cores = [4, 6, 8, 12];

        return {
            cpu: cpus[Math.floor(Math.random() * cpus.length)],
            ram: rams[Math.floor(Math.random() * rams.length)],
            cores: cores[Math.floor(Math.random() * cores.length)]
        };
    }

    /**
     * Generate WebGL renderer specifications
     */
    generateWebGLSpecs() {
        const renderers = [
            'ANGLE (NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (NVIDIA GeForce RTX 2070 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0)'
        ];

        const vendors = [
            'Google Inc. (NVIDIA)',
            'Google Inc. (AMD)',
            'Google Inc. (Intel)'
        ];

        return {
            vendor: vendors[Math.floor(Math.random() * vendors.length)],
            renderer: renderers[Math.floor(Math.random() * renderers.length)]
        };
    }

    /**
     * Generate canvas fingerprint data
     */
    generateCanvasFingerprint() {
        return {
            hash: Math.random().toString(36).substring(2, 15),
            noise: Math.floor(Math.random() * 10) + 1
        };
    }

    /**
     * Generate audio fingerprint data
     */
    generateAudioFingerprint() {
        return {
            hash: Math.random().toString(36).substring(2, 15),
            sampleRate: [44100, 48000][Math.floor(Math.random() * 2)]
        };
    }

    /**
     * Auto arrange and resize browser windows
     */
    async arrangeBrowserWindows() {
        try {
            const runningInstances = Array.from(this.instances.values());
            const instanceCount = runningInstances.length;

            if (instanceCount <= 1) {
                return {
                    success: true,
                    message: 'Single window, no arrangement needed',
                    windowCount: instanceCount
                };
            }

            this.logger.info(`🔧 Arranging ${instanceCount} browser windows...`);

            // Calculate optimal window size and positions
            const screenWidth = 1920; // Assume 1920x1080 screen
            const screenHeight = 1080;
            const taskbarHeight = 40;
            const availableHeight = screenHeight - taskbarHeight;

            let windowWidth, windowHeight, cols, rows;

            // Determine grid layout based on number of windows - IMPROVED SIZING
            if (instanceCount <= 2) {
                cols = 2; rows = 1;
                windowWidth = Math.floor(screenWidth / 2);
                windowHeight = availableHeight;
            } else if (instanceCount <= 4) {
                cols = 2; rows = 2;
                windowWidth = Math.floor(screenWidth / 2);
                windowHeight = Math.floor(availableHeight / 2);
            } else if (instanceCount <= 6) {
                cols = 3; rows = 2;
                windowWidth = Math.floor(screenWidth / 3);
                windowHeight = Math.floor(availableHeight / 2);
            } else if (instanceCount <= 9) {
                cols = 3; rows = 3;
                windowWidth = Math.floor(screenWidth / 3);
                windowHeight = Math.floor(availableHeight / 3);
            } else if (instanceCount <= 12) {
                // 4x3 grid for better visibility
                cols = 4; rows = 3;
                windowWidth = Math.floor(screenWidth / 4);
                windowHeight = Math.floor(availableHeight / 3);
            } else {
                // For more windows, use minimum viable size
                cols = Math.min(6, Math.ceil(Math.sqrt(instanceCount)));
                rows = Math.ceil(instanceCount / cols);
                windowWidth = Math.max(400, Math.floor(screenWidth / cols)); // Min 400px width
                windowHeight = Math.max(300, Math.floor(availableHeight / rows)); // Min 300px height
            }

            // Arrange each browser window
            for (let i = 0; i < runningInstances.length; i++) {
                const instance = runningInstances[i];

                try {
                    const pages = await instance.browser.pages();
                    if (pages.length > 0) {
                        const page = pages[0];

                        // Calculate position
                        const col = i % cols;
                        const row = Math.floor(i / cols);
                        const x = col * windowWidth;
                        const y = row * windowHeight;

                        // Set window position and size
                        await page.setViewport({
                            width: windowWidth - 10, // Small margin
                            height: windowHeight - 50 // Account for browser UI
                        });

                        // Use CDP to set window bounds
                        const client = await page.target().createCDPSession();

                        // Get the actual window ID
                        const windows = await client.send('Browser.getWindowForTarget', {
                            targetId: page.target()._targetId
                        });

                        const windowId = windows.windowId;

                        await client.send('Browser.setWindowBounds', {
                            windowId: windowId,
                            bounds: {
                                left: x,
                                top: y,
                                width: windowWidth,
                                height: windowHeight
                            }
                        });

                        // DON'T minimize windows - keep them visible for user
                        // User wants to see all browsers, not minimize them
                        this.logger.info(`👁️ Window ${i + 1} kept visible for user interaction`);

                        this.logger.info(`📐 Window ${i + 1} positioned at (${x}, ${y}) size ${windowWidth}x${windowHeight}`);
                    }
                } catch (error) {
                    this.logger.warn(`⚠️ Failed to arrange window for ${instance.account.email}:`, error.message);
                }
            }

            this.logger.info(`✅ Browser windows arranged in ${cols}x${rows} grid`);

            return {
                success: true,
                message: `${instanceCount} browsers arranged in ${cols}x${rows} grid`,
                windowCount: instanceCount,
                layout: { cols, rows },
                windowSize: { width: windowWidth, height: windowHeight }
            };

        } catch (error) {
            this.logger.error('❌ Error arranging browser windows:', error);
            return {
                success: false,
                message: 'Failed to arrange browser windows',
                error: error.message
            };
        }
    }

    /**
     * Setup WebRTC Leak Shield extension
     */
    async setupWebRTCExtension() {
        try {
            const extensionsDir = path.join(__dirname, '../../extensions');
            const webrtcExtensionDir = path.join(extensionsDir, 'webrtc-leak-shield');

            // Create extensions directory if not exists
            if (!fs.existsSync(extensionsDir)) {
                fs.mkdirSync(extensionsDir, { recursive: true });
            }

            // Check if extension already exists
            if (fs.existsSync(webrtcExtensionDir)) {
                return webrtcExtensionDir;
            }

            this.logger.info('📦 Setting up WebRTC Leak Shield extension...');

            // Create extension directory
            fs.mkdirSync(webrtcExtensionDir, { recursive: true });

            // Create manifest.json
            const manifest = {
                "manifest_version": 3,
                "name": "WebRTC Leak Shield",
                "version": "1.0.0",
                "description": "Prevent WebRTC IP leaks",
                "permissions": ["privacy"],
                "background": {
                    "service_worker": "background.js"
                },
                "content_scripts": [{
                    "matches": ["<all_urls>"],
                    "js": ["content.js"],
                    "run_at": "document_start"
                }]
            };

            fs.writeFileSync(
                path.join(webrtcExtensionDir, 'manifest.json'),
                JSON.stringify(manifest, null, 2)
            );

            // Create background.js
            const backgroundScript = `
chrome.privacy.network.webRTCIPHandlingPolicy.set({
    value: 'disable_non_proxied_udp'
});

chrome.privacy.network.webRTCMultipleRoutesEnabled.set({
    value: false
});

console.log('WebRTC Leak Shield: IP leak protection enabled');
            `;

            fs.writeFileSync(
                path.join(webrtcExtensionDir, 'background.js'),
                backgroundScript
            );

            // Create content.js
            const contentScript = `
// Override WebRTC methods to prevent IP leaks
(function() {
    'use strict';

    const originalRTCPeerConnection = window.RTCPeerConnection;

    // Override RTCPeerConnection
    window.RTCPeerConnection = function(config) {
        if (config && config.iceServers) {
            config.iceServers = [];
        }
        return new originalRTCPeerConnection(config);
    };

    // Override getUserMedia
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
        navigator.mediaDevices.getUserMedia = function(constraints) {
            return Promise.reject(new Error('WebRTC blocked'));
        };
    }

    console.log('WebRTC Leak Shield: Content script loaded');
})();
            `;

            fs.writeFileSync(
                path.join(webrtcExtensionDir, 'content.js'),
                contentScript
            );

            this.logger.info('✅ WebRTC Leak Shield extension created');
            return webrtcExtensionDir;

        } catch (error) {
            this.logger.error('❌ Error setting up WebRTC extension:', error);
            return '';
        }
    }

    /**
     * Get profile path for account
     */
    getProfilePath(accountId) {
        return path.join(this.profilesDir, accountId);
    }

    /**
     * Get proxy for account - FORCE US PROXY VALIDATION
     */
    async getProxyForAccount(account) {
        try {
            // ALWAYS re-validate proxy assignments for US location
            this.logger.info(`🔍 Validating proxy assignment for account: ${account.email}`);

            // If account has existing proxy, validate it's US and working
            if (account.proxyId) {
                const Database = require('../database');
                const database = new Database();
                const proxies = await database.getProxies();
                const existingProxy = proxies.find(p => p.id === account.proxyId);

                if (existingProxy) {
                    this.logger.info(`🧪 Re-testing existing proxy: ${existingProxy.ip}:${existingProxy.port}`);
                    const testResult = await this.testProxyForUSLocation(existingProxy);

                    if (testResult.success && testResult.isUS) {
                        this.logger.info(`✅ Existing proxy validated: ${existingProxy.ip}:${existingProxy.port} (${testResult.country})`);
                        return existingProxy;
                    } else {
                        this.logger.warn(`❌ Existing proxy failed validation: ${existingProxy.ip}:${existingProxy.port}`);
                        // Clear invalid proxy assignment
                        await database.updateAccount(account.id, { proxyId: null });
                    }
                }
            }

            // Try to auto-assign a working US proxy
            this.logger.info(`🔄 Auto-assigning new US proxy for: ${account.email}`);
            const autoProxy = await this.autoAssignProxy(account);
            if (autoProxy) {
                this.logger.info(`✅ New US proxy assigned: ${autoProxy.ip}:${autoProxy.port}`);
                return autoProxy;
            }

            this.logger.error(`❌ No working US proxies available for: ${account.email}`);
            return null;
        } catch (error) {
            this.logger.error(`Failed to get proxy for account ${account.id}:`, error);
            return null;
        }
    }

    /**
     * Auto-assign an available US proxy to account (ONLY WORKING US PROXIES)
     */
    async autoAssignProxy(account) {
        try {
            const Database = require('../database');
            const database = new Database();

            // Get all available proxies
            const proxies = await database.getProxies();
            if (!proxies || proxies.length === 0) {
                this.logger.warn('⚠️ No proxies available for auto-assignment');
                return null;
            }

            this.logger.info(`🔍 Testing ${proxies.length} proxies for US location and connectivity...`);

            // Get all accounts to check proxy usage
            const accounts = await database.getAccounts();
            const usedProxyIds = accounts
                .filter(acc => acc.proxyId)
                .map(acc => acc.proxyId);

            // Filter and test proxies for US location and working status
            const workingUSProxies = [];

            for (const proxy of proxies) {
                // Skip already used proxies
                if (usedProxyIds.includes(proxy.id)) {
                    continue;
                }

                this.logger.info(`🧪 Testing proxy: ${proxy.ip}:${proxy.port}`);

                // Test proxy connectivity and location
                const testResult = await this.testProxyForUSLocation(proxy);

                if (testResult.success && testResult.isUS) {
                    this.logger.info(`✅ US proxy working: ${proxy.ip}:${proxy.port} (${testResult.country})`);
                    workingUSProxies.push(proxy);
                } else {
                    this.logger.warn(`❌ Proxy rejected: ${proxy.ip}:${proxy.port} - ${testResult.error || 'Not US location'}`);
                }
            }

            if (workingUSProxies.length === 0) {
                this.logger.error('❌ No working US proxies found for assignment');
                return null;
            }

            // Select first working US proxy
            const selectedProxy = workingUSProxies[0];

            this.logger.info(`✅ Auto-assigning working US proxy: ${selectedProxy.ip}:${selectedProxy.port}`);
            await database.updateAccount(account.id, { proxyId: selectedProxy.id });

            return selectedProxy;

        } catch (error) {
            this.logger.error('❌ Error auto-assigning proxy:', error);
            return null;
        }
    }

    /**
     * Sanitize fingerprint for frontend (remove sensitive data)
     */
    sanitizeFingerprint(fingerprint) {
        return {
            viewport: fingerprint.viewport,
            language: fingerprint.language,
            timezone: fingerprint.timezone,
            userAgent: fingerprint.userAgent.substring(0, 50) + '...'
        };
    }

    /**
     * Login to Facebook
     */
    async loginFacebook(account) {
        try {
            const instance = this.instances.get(account.id);
            if (!instance) {
                throw new Error('Browser not running. Please launch browser first.');
            }

            this.logger.info(`🔐 Logging into Facebook for: ${account.email}`);
            this.logger.info(`📊 Account data:`, {
                id: account.id,
                email: account.email,
                hasPassword: !!account.password,
                passwordLength: account.password ? account.password.length : 0,
                has2FA: !!account.twoFA
            });

            const browser = instance.browser;
            const pages = await browser.pages();
            let page = pages[0];

            if (!page) {
                page = await browser.newPage();
            }

            // Navigate to Facebook with human-like behavior
            await page.goto('https://www.facebook.com', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Simple delay
            await page.waitForTimeout(2000);

            // Wait for login form with better error handling
            try {
                await page.waitForSelector('input[name="email"]', { timeout: 10000 });
                this.logger.info('✅ Login form found');
            } catch (error) {
                this.logger.error('❌ Login form not found:', error.message);
                throw new Error('Facebook login form not found - page may have changed');
            }

            // Simple form filling
            await page.click('input[name="email"]');
            await page.evaluate(() => document.querySelector('input[name="email"]').value = '');
            await page.waitForTimeout(300);

            // SLOW human-like typing to avoid captcha
            if (!account.email) {
                throw new Error('Account email is missing');
            }
            await page.type('input[name="email"]', account.email, { delay: this.randomDelay(150, 300) });
            await page.waitForTimeout(this.randomDelay(1000, 2000));
            this.logger.info('✅ Email filled slowly');

            // Move to password field with human delay
            await page.click('input[name="pass"]');
            await page.waitForTimeout(this.randomDelay(1000, 2000));

            // Type password SLOWLY like human
            if (!account.password) {
                throw new Error('Account password is missing');
            }
            await page.type('input[name="pass"]', account.password, { delay: this.randomDelay(150, 300) });
            await page.waitForTimeout(this.randomDelay(1500, 2500));
            this.logger.info('✅ Password filled slowly');

            // Human-like login button click with longer delay
            await page.waitForTimeout(this.randomDelay(2000, 3000));

            const loginButtonClicked = await page.evaluate(() => {
                const buttons = [
                    'button[name="login"]',
                    'button[data-testid="royal_login_button"]',
                    'input[type="submit"][value="Log In"]',
                    'button[type="submit"]'
                ];

                for (const selector of buttons) {
                    const button = document.querySelector(selector);
                    if (button) {
                        button.click();
                        return true;
                    }
                }
                return false;
            });

            if (!loginButtonClicked) {
                throw new Error('Login button not found');
            }
            this.logger.info('✅ Login button clicked');

            // Wait for response
            await page.waitForTimeout(3000);

            // Wait for navigation or 2FA/captcha with better timeout
            await page.waitForTimeout(3000); // Give time for initial response

            try {
                await page.waitForNavigation({
                    waitUntil: 'networkidle2',
                    timeout: 10000
                });
                this.logger.info('✅ Navigation completed');
            } catch (error) {
                this.logger.info('⚠️ Navigation timeout - checking for 2FA/captcha/errors');
            }

            // Check current URL and page content to determine login status
            const currentUrl = page.url();
            this.logger.info(`🔍 Current URL after login attempt: ${currentUrl}`);

            // Check for various login scenarios
            const pageChecks = await page.evaluate(() => {
                return {
                    hasLoginError: !!(document.querySelector('[data-testid="royal_login_error"]') ||
                                    document.querySelector('.login_error_box') ||
                                    document.querySelector('div[role="alert"]')),
                    has2FAInput: !!(document.querySelector('input[name="approvals_code"]') ||
                                  document.querySelector('[data-testid="2fa_code_input"]') ||
                                  document.querySelector('input[placeholder*="code"]') ||
                                  document.querySelector('input[aria-label*="code"]') ||
                                  window.location.href.includes('two_step_verification') ||
                                  window.location.href.includes('2fa') ||
                                  document.title.includes('Two-Factor')),
                    hasCaptcha: !!(document.querySelector('[data-testid="captcha"]') ||
                                 document.querySelector('.captcha')),
                    hasUserMenu: !!(document.querySelector('[data-testid="blue_bar_profile_link"]') ||
                                  document.querySelector('[aria-label="Account"]') ||
                                  document.querySelector('[data-testid="nav-user-menu"]')),
                    isHomePage: window.location.pathname === '/' || window.location.pathname.includes('/home'),
                    errorText: document.querySelector('[data-testid="royal_login_error"]')?.textContent ||
                              document.querySelector('.login_error_box')?.textContent || ''
                };
            });

            this.logger.info('🔍 Page checks:', pageChecks);

            // Determine login status based on checks - IMPROVED DETECTION
            const isLoggedIn = pageChecks.hasUserMenu ||
                              (pageChecks.isHomePage && !currentUrl.includes('login')) ||
                              currentUrl.includes('facebook.com/') && !currentUrl.includes('login') && !currentUrl.includes('checkpoint') ||
                              currentUrl === 'https://www.facebook.com/' ||
                              currentUrl.includes('/home');

            if (isLoggedIn) {
                // Handle post-login popups before declaring success
                await this.handlePostLoginPopups(page);

                // Successfully logged in
                this.logger.info(`✅ Facebook login successful for: ${account.email}`);

                // ENABLE PROXY AFTER SUCCESSFUL LOGIN (VN IP FIRST STRATEGY)
                if (instance.proxy) {
                    this.logger.info('🔄 Enabling US proxy after successful login...');
                    const proxyEnabled = await this.enableProxyAfterLogin(account.id, instance.proxy);
                    if (proxyEnabled) {
                        this.logger.info(`✅ US Proxy enabled: ${instance.proxy.ip}:${instance.proxy.port}`);
                    } else {
                        this.logger.warn(`⚠️ Failed to enable proxy for: ${account.email}`);
                    }
                }

                return {
                    success: true,
                    status: 'logged_in',
                    url: currentUrl
                };
            } else if (pageChecks.has2FAInput || currentUrl.includes('checkpoint')) {
                // 2FA required
                this.logger.info(`🔐 2FA DETECTED for: ${account.email}`);
                this.logger.info(`🔑 Auto-filling 2FA code...`);

                if (account.twoFA) {
                    // Handle 2FA automatically
                    this.logger.info(`✅ 2FA code available, processing automatically...`);
                    return await this.handle2FA(page, account);
                } else {
                    this.logger.warn(`❌ No 2FA code configured for: ${account.email}`);
                    return {
                        success: false,
                        status: '2fa_required',
                        message: '2FA code required but not configured in account settings',
                        url: currentUrl
                    };
                }
            } else if (pageChecks.hasCaptcha) {
                // Captcha detected - return captcha status for auto-login to handle
                this.logger.warn(`🤖 CAPTCHA DETECTED for: ${account.email}`);
                this.logger.warn(`⚠️ Skipping account for now - will retry later`);

                return {
                    success: false,
                    status: 'captcha_required',
                    message: 'Captcha detected - account skipped for auto-retry',
                    url: currentUrl
                };
                this.logger.info(`👤 Please solve the captcha manually...`);
                this.logger.info(`🤖 System will auto-detect when solved and continue with 2FA`);

                const captchaResult = await this.waitForCaptchaSolvedAndContinue(page, account);
                return captchaResult;
            } else if (pageChecks.hasLoginError) {
                // Login error detected
                this.logger.error(`❌ Login error for: ${account.email} - ${pageChecks.errorText}`);
                return {
                    success: false,
                    status: 'login_error',
                    message: pageChecks.errorText || 'Login failed - check credentials',
                    url: currentUrl
                };
            } else {
                // Unknown state - login likely failed
                this.logger.error(`❌ Facebook login failed for: ${account.email} - Unknown state`);
                return {
                    success: false,
                    status: 'login_failed',
                    message: 'Login failed - check credentials or try again',
                    url: currentUrl
                };
            }

        } catch (error) {
            this.logger.error(`❌ Facebook login error for ${account.email}:`, {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            return {
                success: false,
                status: 'error',
                message: error.message,
                error: error.name
            };
        }
    }

    /**
     * Handle 2FA authentication
     */
    async handle2FA(page, account) {
        try {
            this.logger.info(`🔐 Handling 2FA for: ${account.email}`);

            // Wait for 2FA input field - try multiple selectors
            let inputSelector = null;
            const possibleSelectors = [
                'input[name="approvals_code"]',
                'input[data-testid="2fa_code_input"]',
                'input[placeholder*="code"]',
                'input[aria-label*="code"]',
                'input[type="text"]',
                'input[type="number"]'
            ];

            for (const selector of possibleSelectors) {
                try {
                    await page.waitForSelector(selector, { timeout: 2000 });
                    inputSelector = selector;
                    this.logger.info(`✅ Found 2FA input: ${selector}`);
                    break;
                } catch (error) {
                    this.logger.info(`⏳ Trying selector: ${selector}`);
                }
            }

            if (!inputSelector) {
                throw new Error('2FA input field not found with any selector');
            }

            await page.waitForTimeout(this.randomDelay(800, 1500));

            // Human-like 2FA code entry
            await page.click(inputSelector);
            await page.waitForTimeout(this.randomDelay(300, 700));

            // Get 2FA code from 2fa.live or generate from secret
            let twoFACode;

            if (account.twoFA.startsWith('http') || account.twoFA.includes('2fa.live')) {
                // Get from 2fa.live API
                twoFACode = await this.get2FAFromAPI(account.twoFA);
            } else {
                // Generate from TOTP secret
                twoFACode = this.generate2FACode(account.twoFA);
            }

            this.logger.info(`🔑 Using 2FA code: ${twoFACode}`);

            // Type 2FA code SLOWLY like human
            await page.type(inputSelector, twoFACode, { delay: this.randomDelay(200, 400) });
            await page.waitForTimeout(this.randomDelay(1000, 2000));
            this.logger.info('✅ 2FA code entered slowly');
            await page.waitForTimeout(this.randomDelay(1000, 2000));

            // AUTO CLICK CONTINUE/SUBMIT BUTTON
            const submitButtonClicked = await page.evaluate(() => {
                const buttons = [
                    'button[type="submit"]',
                    'button[data-testid="2fa_submit_button"]',
                    'input[type="submit"]',
                    '[role="button"]'
                ];

                // Also check for buttons with specific text content
                const textButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
                    const text = btn.textContent.toLowerCase();
                    return text.includes('continue') || text.includes('tiếp tục') ||
                           text.includes('submit') || text.includes('confirm') ||
                           text.includes('xác nhận');
                });

                // Try CSS selectors first
                for (const selector of buttons) {
                    const button = document.querySelector(selector);
                    if (button && button.offsetParent !== null) {
                        button.click();
                        return true;
                    }
                }

                // Try text-based buttons
                for (const button of textButtons) {
                    if (button && button.offsetParent !== null) {
                        button.click();
                        return true;
                    }
                }

                // Final fallback: find any clickable button
                const allButtons = document.querySelectorAll('button, input[type="submit"], [role="button"]');
                for (const button of allButtons) {
                    if (button.offsetParent !== null &&
                        (button.textContent.includes('Continue') ||
                         button.textContent.includes('Tiếp tục') ||
                         button.textContent.includes('Submit') ||
                         button.textContent.includes('Confirm') ||
                         button.textContent.includes('Xác nhận'))) {
                        button.click();
                        return true;
                    }
                }

                return false;
            });

            if (submitButtonClicked) {
                this.logger.info('✅ 2FA submit button clicked automatically');
            } else {
                this.logger.warn('⚠️ 2FA submit button not found - trying Enter key');
                await page.keyboard.press('Enter');
                this.logger.info('✅ Pressed Enter key as fallback');
            }

            // Wait for navigation
            await page.waitForNavigation({
                waitUntil: 'networkidle2',
                timeout: 15000
            });

            // Handle post-login popups (Save password, Trust device, etc.)
            await this.handlePostLoginPopups(page);

            const currentUrl = page.url();

            if (currentUrl.includes('facebook.com') && !currentUrl.includes('checkpoint')) {
                this.logger.info(`✅ 2FA successful for: ${account.email}`);
                return {
                    success: true,
                    status: 'logged_in',
                    url: currentUrl
                };
            } else {
                this.logger.error(`❌ 2FA failed for: ${account.email}`);
                return {
                    success: false,
                    status: '2fa_failed',
                    message: 'Invalid 2FA code',
                    url: currentUrl
                };
            }

        } catch (error) {
            this.logger.error(`❌ 2FA error for ${account.email}:`, error);
            throw error;
        }
    }

    /**
     * Close all browsers
     */
    async closeAllBrowsers() {
        const promises = [];
        for (const accountId of this.instances.keys()) {
            promises.push(this.closeBrowser(accountId));
        }
        await Promise.all(promises);
        this.logger.info('✅ All browsers closed');
    }

    /**
     * Generate random delay between min and max milliseconds
     */
    randomDelay(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * Get random user agent to avoid detection
     */
    getRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
        ];
        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }

    /**
     * Get random viewport to avoid detection
     */
    getRandomViewport() {
        const viewports = [
            { width: 1920, height: 1080 },
            { width: 1366, height: 768 },
            { width: 1440, height: 900 },
            { width: 1536, height: 864 },
            { width: 1280, height: 720 }
        ];
        return viewports[Math.floor(Math.random() * viewports.length)];
    }

    /**
     * Human-like typing with random delays between keystrokes
     */
    async humanType(page, text) {
        // Safety check
        if (!text || typeof text !== 'string') {
            this.logger.warn('⚠️ Invalid text for humanType:', text);
            return;
        }

        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            await page.keyboard.type(char);

            // Random delay between keystrokes (50-200ms)
            const delay = this.randomDelay(50, 200);

            // Occasionally longer pauses (simulate thinking)
            if (Math.random() < 0.1) {
                await page.waitForTimeout(this.randomDelay(300, 800));
            } else {
                await page.waitForTimeout(delay);
            }
        }
    }

    /**
     * Simulate human mouse movement
     */
    async humanMouseMove(page, selector) {
        try {
            const element = await page.$(selector);
            if (element) {
                const box = await element.boundingBox();
                if (box) {
                    // Move to random point within element
                    const x = box.x + Math.random() * box.width;
                    const y = box.y + Math.random() * box.height;

                    await page.mouse.move(x, y, { steps: this.randomDelay(5, 15) });
                    await page.waitForTimeout(this.randomDelay(100, 300));
                }
            }
        } catch (error) {
            this.logger.warn('Mouse movement failed:', error.message);
        }
    }

    /**
     * Handle post-login popups (Save password, Trust device, etc.)
     */
    async handlePostLoginPopups(page) {
        try {
            this.logger.info('🔍 Checking for post-login popups...');

            // Wait a bit for popups to appear
            await page.waitForTimeout(2000);

            // Handle "Save login info" popup with comprehensive detection
            const savePasswordHandled = await page.evaluate(() => {
                // Check for save login info dialog texts
                const dialogTexts = [
                    'Lưu thông tin đăng nhập vào Facebook',
                    'Save your login info',
                    'Remember login info',
                    'Save password',
                    'Lưu mật khẩu',
                    'Ghi nhớ thông tin đăng nhập',
                    'để bạn không cần nhập vào lần sau'
                ];

                let foundDialog = false;
                for (const text of dialogTexts) {
                    if (document.body.innerText.includes(text)) {
                        foundDialog = true;
                        console.log('🔍 Found save login dialog:', text);
                        break;
                    }
                }

                if (foundDialog) {
                    // Look for save buttons with comprehensive selectors
                    const saveSelectors = [
                        'button[data-testid="save_password_button"]',
                        'button[data-testid="save_login_button"]',
                        '[data-testid="save_password_button"]',
                        '[data-testid="save_login_button"]',
                        'button[type="submit"]',
                        'input[type="submit"]'
                    ];

                    // Try data-testid selectors first
                    for (const selector of saveSelectors) {
                        try {
                            const button = document.querySelector(selector);
                            if (button && button.offsetParent !== null) {
                                button.click();
                                console.log('✅ Clicked save button via testid:', selector);
                                return true;
                            }
                        } catch (e) {}
                    }

                    // Enhanced fallback: find any clickable element with save text
                    const allClickables = document.querySelectorAll('button, div[role="button"], a, span[role="button"], input[type="button"]');
                    for (const element of allClickables) {
                        if (element.offsetParent !== null) {
                            const text = element.textContent.trim().toLowerCase();
                            const value = element.value ? element.value.toLowerCase() : '';

                            if (text === 'lưu' || text === 'save' ||
                                text.includes('lưu thông tin') ||
                                text.includes('save login') ||
                                text.includes('save password') ||
                                text.includes('ghi nhớ') ||
                                value === 'lưu' || value === 'save') {
                                element.click();
                                console.log('✅ Clicked save button via text:', text || value);
                                return true;
                            }
                        }
                    }

                    // Final fallback: look for primary action buttons in dialogs
                    const primaryButtons = document.querySelectorAll('[role="dialog"] button, .dialog button, [data-testid*="dialog"] button');
                    for (const button of primaryButtons) {
                        if (button.offsetParent !== null) {
                            const text = button.textContent.trim().toLowerCase();
                            if (text === 'lưu' || text === 'save' || text === 'ok' || text === 'đồng ý') {
                                button.click();
                                console.log('✅ Clicked dialog button:', text);
                                return true;
                            }
                        }
                    }
                }

                return false;
            });

            if (savePasswordHandled) {
                this.logger.info('✅ Clicked "Save password" button');
                await page.waitForTimeout(2000);
            }

            // Handle "Trust this device" popup with comprehensive detection
            const trustDeviceHandled = await page.evaluate(() => {
                // Check for trust device dialog texts
                const trustTexts = [
                    'Trust this device',
                    'Tin cậy thiết bị này',
                    'Remember this device',
                    'Ghi nhớ thiết bị này',
                    'Trust browser',
                    'Tin cậy trình duyệt'
                ];

                let foundTrustDialog = false;
                for (const text of trustTexts) {
                    if (document.body.innerText.includes(text)) {
                        foundTrustDialog = true;
                        console.log('🔍 Found trust device dialog:', text);
                        break;
                    }
                }

                if (foundTrustDialog) {
                    // Look for trust buttons with comprehensive selectors
                    const trustSelectors = [
                        'button[data-testid="trust_device_button"]',
                        'button[data-testid="trust_browser_button"]',
                        '[data-testid="trust_device_button"]',
                        '[data-testid="trust_browser_button"]',
                        'button[type="submit"]',
                        'input[type="submit"]'
                    ];

                    // Try data-testid selectors first
                    for (const selector of trustSelectors) {
                        try {
                            const button = document.querySelector(selector);
                            if (button && button.offsetParent !== null) {
                                button.click();
                                console.log('✅ Clicked trust button via testid:', selector);
                                return true;
                            }
                        } catch (e) {}
                    }

                    // Enhanced fallback: find any clickable element with trust text
                    const allClickables = document.querySelectorAll('button, div[role="button"], a, span[role="button"], input[type="button"]');
                    for (const element of allClickables) {
                        if (element.offsetParent !== null) {
                            const text = element.textContent.trim().toLowerCase();
                            const value = element.value ? element.value.toLowerCase() : '';

                            if (text.includes('trust') || text.includes('tin cậy') ||
                                text.includes('remember') || text.includes('ghi nhớ') ||
                                text.includes('thiết bị') || text === 'ok' || text === 'đồng ý' ||
                                value.includes('trust') || value.includes('tin cậy')) {
                                element.click();
                                console.log('✅ Clicked trust button via text:', text || value);
                                return true;
                            }
                        }
                    }

                    // Final fallback: look for primary action buttons in trust dialogs
                    const primaryButtons = document.querySelectorAll('[role="dialog"] button, .dialog button, [data-testid*="dialog"] button');
                    for (const button of primaryButtons) {
                        if (button.offsetParent !== null) {
                            const text = button.textContent.trim().toLowerCase();
                            if (text === 'ok' || text === 'đồng ý' || text === 'continue' || text === 'tiếp tục') {
                                button.click();
                                console.log('✅ Clicked trust dialog button:', text);
                                return true;
                            }
                        }
                    }
                }

                return false;
            });

            if (trustDeviceHandled) {
                this.logger.info('✅ Clicked "Trust device" button');
                await page.waitForTimeout(2000);
            }

            // Handle "Not Now" or "Later" buttons for notifications
            const notNowHandled = await page.evaluate(() => {
                const notNowTexts = [
                    'Not Now',
                    'Không phải bây giờ',
                    'Later',
                    'Để sau',
                    'Skip',
                    'Bỏ qua',
                    'Turn on notifications',
                    'Bật thông báo',
                    'Allow notifications',
                    'Cho phép thông báo'
                ];

                let foundNotificationDialog = false;
                for (const text of notNowTexts) {
                    if (document.body.innerText.includes(text)) {
                        foundNotificationDialog = true;
                        console.log('🔍 Found notification dialog:', text);
                        break;
                    }
                }

                if (foundNotificationDialog) {
                    // Try specific selectors first
                    const notNowSelectors = [
                        'button[data-testid="not_now_button"]',
                        'button[data-testid="cancel_button"]',
                        '[data-testid="not_now_button"]',
                        '[data-testid="cancel_button"]'
                    ];

                    for (const selector of notNowSelectors) {
                        try {
                            const button = document.querySelector(selector);
                            if (button && button.offsetParent !== null) {
                                button.click();
                                console.log('✅ Clicked not now button via testid:', selector);
                                return true;
                            }
                        } catch (e) {}
                    }

                    // Fallback to text-based search
                    const allClickables = document.querySelectorAll('button, div[role="button"], a, span[role="button"], input[type="button"]');
                    for (const element of allClickables) {
                        if (element.offsetParent !== null) {
                            const text = element.textContent.trim().toLowerCase();
                            const value = element.value ? element.value.toLowerCase() : '';

                            if (text === 'not now' || text === 'không phải bây giờ' ||
                                text === 'later' || text === 'để sau' ||
                                text === 'skip' || text === 'bỏ qua' ||
                                text === 'cancel' || text === 'hủy' ||
                                value === 'not now' || value === 'cancel') {
                                element.click();
                                console.log('✅ Clicked "Not Now" button:', text || value);
                                return true;
                            }
                        }
                    }
                }

                return false;
            });

            if (notNowHandled) {
                this.logger.info('✅ Clicked "Not Now" button');
                await page.waitForTimeout(2000);
            }

            // Handle any "Continue" or "OK" buttons
            const continueHandled = await page.evaluate(() => {
                const continueSelectors = [
                    'button[data-testid="continue_button"]',
                    'button[data-testid="ok_button"]',
                    '[data-testid="continue_button"]',
                    '[data-testid="ok_button"]'
                ];

                // Try data-testid selectors first
                for (const selector of continueSelectors) {
                    try {
                        const button = document.querySelector(selector);
                        if (button && button.offsetParent !== null) {
                            button.click();
                            console.log('✅ Clicked continue button via testid:', selector);
                            return true;
                        }
                    } catch (e) {}
                }

                // Fallback for continue/ok buttons
                const allClickables = document.querySelectorAll('button, div[role="button"], a, span[role="button"]');
                for (const element of allClickables) {
                    if (element.offsetParent !== null) {
                        const text = element.textContent.trim().toLowerCase();
                        if (text === 'continue' || text === 'tiếp tục' ||
                            text === 'ok' || text === 'đồng ý' ||
                            text === 'done' || text === 'hoàn tất') {
                            element.click();
                            console.log('✅ Clicked continue button via text:', text);
                            return true;
                        }
                    }
                }

                return false;
            });

            if (continueHandled) {
                this.logger.info('✅ Clicked "Continue" button');
                await page.waitForTimeout(2000);
            }

            // Handle location/privacy popups
            const locationHandled = await page.evaluate(() => {
                const locationTexts = [
                    'Share your location',
                    'Chia sẻ vị trí',
                    'Location access',
                    'Truy cập vị trí',
                    'Allow location',
                    'Cho phép vị trí'
                ];

                let foundLocationDialog = false;
                for (const text of locationTexts) {
                    if (document.body.innerText.includes(text)) {
                        foundLocationDialog = true;
                        console.log('🔍 Found location dialog:', text);
                        break;
                    }
                }

                if (foundLocationDialog) {
                    const allClickables = document.querySelectorAll('button, div[role="button"], a, span[role="button"]');
                    for (const element of allClickables) {
                        if (element.offsetParent !== null) {
                            const text = element.textContent.trim().toLowerCase();
                            if (text === 'not now' || text === 'không phải bây giờ' ||
                                text === 'deny' || text === 'từ chối' ||
                                text === 'block' || text === 'chặn') {
                                element.click();
                                console.log('✅ Clicked location deny button:', text);
                                return true;
                            }
                        }
                    }
                }

                return false;
            });

            if (locationHandled) {
                this.logger.info('✅ Handled location permission popup');
                await page.waitForTimeout(2000);
            }

            // Handle any remaining close buttons (X buttons)
            const closeHandled = await page.evaluate(() => {
                const closeSelectors = [
                    'button[aria-label="Close"]',
                    'button[aria-label="Đóng"]',
                    '[data-testid="close_button"]',
                    '.close-button',
                    'button.close'
                ];

                for (const selector of closeSelectors) {
                    try {
                        const button = document.querySelector(selector);
                        if (button && button.offsetParent !== null) {
                            button.click();
                            console.log('✅ Clicked close button:', selector);
                            return true;
                        }
                    } catch (e) {}
                }

                return false;
            });

            if (closeHandled) {
                this.logger.info('✅ Closed remaining popups');
                await page.waitForTimeout(1000);
            }

            this.logger.info('✅ Post-login popup handling completed');

        } catch (error) {
            this.logger.warn('⚠️ Error handling post-login popups:', error.message);
        }
    }

    /**
     * Wait for captcha solved and auto-continue with 2FA
     */
    async waitForCaptchaSolvedAndContinue(page, account) {
        try {
            this.logger.info('🤖 Waiting for captcha to be solved...');
            this.logger.info('👤 Please solve the captcha in the browser');
            this.logger.info('⏳ System will automatically detect and continue...');

            const maxWaitTime = 180000; // 3 minutes
            const checkInterval = 2000; // Check every 2 seconds
            let waitedTime = 0;

            while (waitedTime < maxWaitTime) {
                await page.waitForTimeout(checkInterval);
                waitedTime += checkInterval;

                // Check current page state
                const pageState = await page.evaluate(() => {
                    return {
                        // Captcha checks
                        hasCaptcha: !!(document.querySelector('[data-testid="captcha"]') ||
                                     document.querySelector('.captcha') ||
                                     document.querySelector('#captcha') ||
                                     document.querySelector('iframe[src*="captcha"]') ||
                                     document.querySelector('[role="dialog"]') && document.querySelector('[role="dialog"]').innerText.includes('captcha')),

                        // 2FA checks
                        has2FA: !!(document.querySelector('input[name="approvals_code"]') ||
                                 document.querySelector('[data-testid="2fa_code_input"]') ||
                                 document.querySelector('input[placeholder*="code"]') ||
                                 document.querySelector('input[aria-label*="code"]')),

                        // Success checks
                        isLoggedIn: !!(document.querySelector('[data-testid="blue_bar_profile_link"]') ||
                                     document.querySelector('[aria-label="Account"]') ||
                                     document.querySelector('[data-testid="nav_profile_photo"]') ||
                                     window.location.pathname === '/' ||
                                     window.location.pathname.includes('/home')),

                        // Error checks
                        hasError: !!(document.querySelector('[data-testid="login_error"]') ||
                                   document.querySelector('.login_error_box') ||
                                   document.querySelector('[role="alert"]')),

                        currentUrl: window.location.href,
                        pageTitle: document.title
                    };
                });

                // Progress update
                if (waitedTime % 15000 === 0) {
                    const remaining = Math.round((maxWaitTime - waitedTime) / 1000);
                    this.logger.info(`⏳ Still waiting... ${remaining}s remaining`);
                    this.logger.info(`📊 Page state: captcha=${pageState.hasCaptcha}, 2fa=${pageState.has2FA}, logged=${pageState.isLoggedIn}`);
                }

                // Captcha solved - check what's next
                if (!pageState.hasCaptcha) {
                    this.logger.info('✅ Captcha solved! Checking next step...');

                    if (pageState.has2FA) {
                        // AUTO-HANDLE 2FA
                        this.logger.info('🔐 2FA form detected - auto-filling code...');
                        return await this.handle2FA(page, account);

                    } else if (pageState.isLoggedIn) {
                        // Login successful
                        this.logger.info('🎉 Login successful after captcha!');

                        // ENABLE PROXY AFTER SUCCESSFUL LOGIN
                        const instance = this.instances.get(account.id);
                        if (instance && instance.proxy) {
                            this.logger.info('🔄 Enabling proxy after successful login...');
                            await this.enableProxyAfterLogin(account.id, instance.proxy);
                        }

                        return {
                            success: true,
                            status: 'logged_in',
                            message: 'Login successful after captcha solved',
                            url: pageState.currentUrl
                        };

                    } else if (pageState.hasError) {
                        // Error detected
                        this.logger.error('❌ Login error detected after captcha');
                        return {
                            success: false,
                            status: 'login_error',
                            message: 'Login error after captcha solved',
                            url: pageState.currentUrl
                        };

                    } else {
                        // Continue waiting for page to load
                        this.logger.info('⏳ Captcha solved, waiting for page to load...');
                        await page.waitForTimeout(3000);
                        continue;
                    }
                }
            }

            // Timeout
            this.logger.error('❌ Captcha solving timeout');
            return {
                success: false,
                status: 'captcha_timeout',
                message: 'Captcha was not solved within 3 minutes'
            };

        } catch (error) {
            this.logger.error('❌ Captcha handling error:', error);
            return {
                success: false,
                status: 'captcha_error',
                message: error.message
            };
        }
    }

    /**
     * Wait for user to solve captcha manually, then auto-handle next steps
     */
    async solveCaptcha(page, account) {
        try {
            this.logger.info('🤖 Captcha detected - waiting for manual solution...');
            this.logger.info('👤 Please solve the captcha in the browser');
            this.logger.info('⏳ System will automatically continue after captcha is solved...');

            // Wait for captcha to disappear or timeout
            let captchaSolved = false;
            const maxWaitTime = 120000; // 2 minutes
            const checkInterval = 3000; // Check every 3 seconds
            let waitedTime = 0;

            while (waitedTime < maxWaitTime && !captchaSolved) {
                await page.waitForTimeout(checkInterval);
                waitedTime += checkInterval;

                // Check current page state
                const pageState = await page.evaluate(() => {
                    return {
                        hasCaptcha: !!(document.querySelector('[data-testid="captcha"]') ||
                                     document.querySelector('.captcha') ||
                                     document.querySelector('#captcha') ||
                                     document.querySelector('iframe[src*="captcha"]')),
                        has2FA: !!(document.querySelector('input[name="approvals_code"]') ||
                                 document.querySelector('[data-testid="2fa_code_input"]')),
                        isLoggedIn: !!(document.querySelector('[data-testid="blue_bar_profile_link"]') ||
                                     document.querySelector('[aria-label="Account"]') ||
                                     window.location.pathname === '/' ||
                                     window.location.pathname.includes('/home')),
                        currentUrl: window.location.href
                    };
                });

                // Captcha solved - check what's next
                if (!pageState.hasCaptcha) {
                    this.logger.info('✅ Captcha solved! Checking next step...');

                    if (pageState.has2FA) {
                        // Auto-handle 2FA
                        this.logger.info('🔐 2FA detected - auto-filling code...');
                        return await this.handle2FA(page, account);
                    } else if (pageState.isLoggedIn) {
                        // Login successful
                        this.logger.info('🎉 Login successful after captcha!');
                        return {
                            success: true,
                            status: 'logged_in',
                            message: 'Login successful after captcha solved',
                            url: pageState.currentUrl
                        };
                    } else {
                        // Continue waiting or check for errors
                        this.logger.info('⏳ Captcha solved, waiting for page to load...');
                        await page.waitForTimeout(3000);
                        continue;
                    }
                }

                // Progress update
                if (waitedTime % 15000 === 0) {
                    const remaining = Math.round((maxWaitTime - waitedTime) / 1000);
                    this.logger.info(`⏳ Still waiting for captcha solution... ${remaining}s remaining`);
                }
            }

            // Timeout
            this.logger.error('❌ Captcha solving timeout');
            return {
                success: false,
                status: 'captcha_timeout',
                message: 'Captcha was not solved within 2 minutes'
            };

        } catch (error) {
            this.logger.error('❌ Captcha handling error:', error);
            return {
                success: false,
                status: 'captcha_error',
                message: error.message
            };
        }
    }

    /**
     * Generate 2FA code from secret key
     */
    generate2FACode(secret) {
        try {
            // Simple TOTP implementation
            const crypto = require('crypto');

            // Remove spaces and convert to uppercase
            const cleanSecret = secret.replace(/\s/g, '').toUpperCase();

            // Base32 decode
            const key = this.base32Decode(cleanSecret);

            // Get current time step (30 seconds)
            const timeStep = Math.floor(Date.now() / 1000 / 30);

            // Convert to 8-byte buffer
            const timeBuffer = Buffer.alloc(8);
            timeBuffer.writeUInt32BE(Math.floor(timeStep / 0x100000000), 0);
            timeBuffer.writeUInt32BE(timeStep & 0xffffffff, 4);

            // HMAC-SHA1
            const hmac = crypto.createHmac('sha1', key);
            hmac.update(timeBuffer);
            const hash = hmac.digest();

            // Dynamic truncation
            const offset = hash[hash.length - 1] & 0x0f;
            const code = ((hash[offset] & 0x7f) << 24) |
                        ((hash[offset + 1] & 0xff) << 16) |
                        ((hash[offset + 2] & 0xff) << 8) |
                        (hash[offset + 3] & 0xff);

            // Return 6-digit code
            return (code % 1000000).toString().padStart(6, '0');

        } catch (error) {
            this.logger.error('❌ 2FA generation error:', error);
            // Fallback: use the secret as-is (might be a static code)
            return secret.replace(/\s/g, '').substring(0, 6);
        }
    }

    /**
     * Get 2FA code from 2fa.live API
     */
    async get2FAFromAPI(apiUrl) {
        try {
            this.logger.info('🌐 Fetching 2FA code from 2fa.live...');

            const https = require('https');
            const http = require('http');

            return new Promise((resolve, reject) => {
                const client = apiUrl.startsWith('https') ? https : http;

                const req = client.get(apiUrl, (res) => {
                    let data = '';

                    res.on('data', (chunk) => {
                        data += chunk;
                    });

                    res.on('end', () => {
                        try {
                            // Try to parse as JSON first
                            const jsonData = JSON.parse(data);
                            const code = jsonData.code || jsonData.token || jsonData.otp;

                            if (code) {
                                this.logger.info('✅ 2FA code received from API');
                                resolve(code.toString().replace(/\s/g, ''));
                            } else {
                                throw new Error('No code found in API response');
                            }
                        } catch (parseError) {
                            // If not JSON, treat as plain text
                            const code = data.trim().replace(/\D/g, ''); // Extract digits only
                            if (code.length >= 6) {
                                this.logger.info('✅ 2FA code received from API (plain text)');
                                resolve(code.substring(0, 6));
                            } else {
                                reject(new Error('Invalid 2FA code format from API'));
                            }
                        }
                    });
                });

                req.on('error', (error) => {
                    this.logger.error('❌ 2FA API error:', error);
                    reject(error);
                });

                req.setTimeout(10000, () => {
                    req.destroy();
                    reject(new Error('2FA API timeout'));
                });
            });

        } catch (error) {
            this.logger.error('❌ Failed to get 2FA from API:', error);
            // Fallback to generating from secret if it looks like one
            if (apiUrl.length > 16 && !apiUrl.includes('http')) {
                return this.generate2FACode(apiUrl);
            }
            throw error;
        }
    }

    /**
     * Simple Base32 decoder
     */
    base32Decode(encoded) {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        let bits = '';

        for (let i = 0; i < encoded.length; i++) {
            const char = encoded[i];
            const index = alphabet.indexOf(char);
            if (index === -1) continue;
            bits += index.toString(2).padStart(5, '0');
        }

        const bytes = [];
        for (let i = 0; i < bits.length; i += 8) {
            const byte = bits.substr(i, 8);
            if (byte.length === 8) {
                bytes.push(parseInt(byte, 2));
            }
        }

        return Buffer.from(bytes);
    }

    /**
     * Test proxy for US location and connectivity
     */
    async testProxyForUSLocation(proxy) {
        const axios = require('axios');
        const { HttpsProxyAgent } = require('https-proxy-agent');

        try {
            this.logger.info(`🧪 Testing proxy for US location: ${proxy.ip}:${proxy.port}`);

            // Load settings to get proxy credentials
            const settings = await this.loadSettings();
            const proxyAuth = {
                username: settings.proxyUsername || proxy.username,
                password: settings.proxyPassword || proxy.password
            };

            // Create proxy agent
            const proxyUrl = `http://${proxyAuth.username}:${proxyAuth.password}@${proxy.ip}:${proxy.port}`;
            const agent = new HttpsProxyAgent(proxyUrl);

            // Test connection with geolocation API
            const response = await axios.get('http://ip-api.com/json', {
                httpAgent: agent,
                httpsAgent: agent,
                timeout: 15000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            const result = response.data;

            if (result.status === 'success') {
                const isUS = result.countryCode === 'US';

                this.logger.info(`🌍 Proxy location: ${result.country} (${result.countryCode}) - ${result.city}`);

                if (isUS) {
                    this.logger.info(`✅ US proxy confirmed: ${proxy.ip}:${proxy.port}`);
                } else {
                    this.logger.warn(`❌ Non-US proxy: ${proxy.ip}:${proxy.port} (${result.country})`);
                }

                return {
                    success: true,
                    isUS: isUS,
                    ip: result.query,
                    country: result.country,
                    countryCode: result.countryCode,
                    city: result.city,
                    region: result.regionName
                };
            } else {
                throw new Error('Geolocation API failed');
            }

        } catch (error) {
            this.logger.error(`❌ Proxy test failed: ${error.message}`);
            return {
                success: false,
                isUS: false,
                error: error.message
            };
        }
    }

    /**
     * Test proxy connection before launching browser
     */
    async testProxyConnection(proxy) {
        const axios = require('axios');
        const { HttpsProxyAgent } = require('https-proxy-agent');

        try {
            this.logger.info(`🧪 Testing proxy: ${proxy.ip}:${proxy.port}`);

            // Load settings to get proxy credentials
            const settings = await this.loadSettings();
            const proxyAuth = {
                username: settings.proxyUsername || proxy.username,
                password: settings.proxyPassword || proxy.password
            };

            // Create proxy agent
            const proxyUrl = `http://${proxyAuth.username}:${proxyAuth.password}@${proxy.ip}:${proxy.port}`;
            const agent = new HttpsProxyAgent(proxyUrl);

            // Test connection with timeout
            const response = await axios.get('https://httpbin.org/ip', {
                httpsAgent: agent,
                timeout: 10000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            const detectedIP = response.data.origin;
            this.logger.info(`🌐 Proxy test result: ${detectedIP}`);

            // Check if proxy is working
            if (detectedIP.includes(proxy.ip)) {
                return {
                    success: true,
                    ip: detectedIP,
                    country: 'Unknown'
                };
            } else {
                return {
                    success: false,
                    error: `IP mismatch - Expected: ${proxy.ip}, Got: ${detectedIP}`,
                    detectedIP: detectedIP
                };
            }

        } catch (error) {
            this.logger.error(`❌ Proxy test failed: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Verify proxy is working correctly by checking IP
     */
    async verifyProxyWorking(browser, proxy) {
        try {
            this.logger.info(`🔍 Verifying proxy ${proxy.ip}:${proxy.port} is working...`);

            const pages = await browser.pages();
            const page = pages[0] || await browser.newPage();

            // Navigate to IP checking service
            await page.goto('https://httpbin.org/ip', {
                waitUntil: 'networkidle2',
                timeout: 15000
            });

            // Get the displayed IP
            const ipInfo = await page.evaluate(() => {
                try {
                    const bodyText = document.body.innerText;
                    const jsonData = JSON.parse(bodyText);
                    return jsonData.origin || jsonData.ip || 'Unknown';
                } catch (e) {
                    return document.body.innerText.trim();
                }
            });

            this.logger.info(`🌐 Current IP via proxy: ${ipInfo}`);
            this.logger.info(`🎯 Expected proxy IP: ${proxy.ip}`);

            // Check if IP matches proxy IP
            if (ipInfo.includes(proxy.ip)) {
                this.logger.info(`✅ Proxy working correctly - IP matches: ${proxy.ip}`);
            } else {
                this.logger.error(`❌ PROXY NOT WORKING! Expected: ${proxy.ip}, Got: ${ipInfo}`);

                // Try alternative IP check
                try {
                    await page.goto('https://api.ipify.org', {
                        waitUntil: 'networkidle2',
                        timeout: 10000
                    });

                    const altIP = await page.evaluate(() => document.body.innerText.trim());
                    this.logger.info(`🔄 Alternative IP check: ${altIP}`);

                    if (!altIP.includes(proxy.ip)) {
                        this.logger.error(`❌ CONFIRMED: Proxy failed - Real IP leaking: ${altIP}`);
                        this.logger.error(`🔧 Proxy config: ${proxy.type}://${proxy.username}:${proxy.password}@${proxy.ip}:${proxy.port}`);
                    }
                } catch (altError) {
                    this.logger.error(`❌ Alternative IP check failed: ${altError.message}`);
                }
            }

            // Check for Vietnam IP (common leak indicator)
            if (ipInfo.includes('Vietnam') || ipInfo.includes('VN') ||
                ipInfo.startsWith('14.') || ipInfo.startsWith('27.') ||
                ipInfo.startsWith('42.') || ipInfo.startsWith('113.') ||
                ipInfo.startsWith('116.') || ipInfo.startsWith('171.') ||
                ipInfo.startsWith('210.')) {
                this.logger.warn(`🇻🇳 WARNING: Detected Vietnam IP - Proxy may not be working properly!`);
            }

        } catch (error) {
            this.logger.error('❌ Error verifying proxy:', error.message);
        }
    }

    /**
     * Setup proxy authentication - SIMPLE AND RELIABLE
     */
    async setupProxyAuth(browser, proxy, proxyAuth = null) {
        try {
            // Use provided auth or fallback to proxy auth
            const auth = proxyAuth || { username: proxy.username, password: proxy.password };

            this.logger.info(`🔐 Setting up SIMPLE proxy auth: ${auth.username}:${auth.password}`);

            // Get all pages and setup auth for each
            const pages = await browser.pages();
            for (const page of pages) {
                await page.authenticate({
                    username: auth.username,
                    password: auth.password
                });
                this.logger.info(`✅ Auth configured for page`);
            }

            // Setup auth for new pages
            browser.on('targetcreated', async (target) => {
                if (target.type() === 'page') {
                    try {
                        const newPage = await target.page();
                        if (newPage) {
                            await newPage.authenticate({
                                username: auth.username,
                                password: auth.password
                            });
                            this.logger.info(`✅ Auth setup for new page`);
                        }
                    } catch (error) {
                        this.logger.warn(`⚠️ Failed to setup auth for new page: ${error.message}`);
                    }
                }
            });

            this.logger.info('✅ Simple proxy authentication configured successfully');

        } catch (error) {
            this.logger.error('❌ Proxy auth setup failed:', error);
            throw error;
        }
    }

    /**
     * Setup advanced anti-captcha features
     */
    async setupAntiCaptchaFeatures(browser) {
        try {
            this.logger.info('🛡️ Setting up advanced anti-captcha features...');

            const pages = await browser.pages();
            const page = pages[0] || await browser.newPage();

            // REMOVE AUTOMATION TRACES
            await page.evaluateOnNewDocument(() => {
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // Mock chrome runtime
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };

                // Mock plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                // Mock languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                // Mock permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );

                // Mock getUserMedia
                const getParameter = WebGLRenderingContext.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) {
                        return 'Intel Inc.';
                    }
                    if (parameter === 37446) {
                        return 'Intel Iris OpenGL Engine';
                    }
                    return getParameter(parameter);
                };
            });

            // HUMAN-LIKE MOUSE MOVEMENTS
            await page.evaluateOnNewDocument(() => {
                let mouseX = 0;
                let mouseY = 0;

                document.addEventListener('mousemove', (e) => {
                    mouseX = e.clientX;
                    mouseY = e.clientY;
                });

                // Add random mouse movements
                setInterval(() => {
                    if (Math.random() < 0.1) { // 10% chance every interval
                        const event = new MouseEvent('mousemove', {
                            clientX: mouseX + (Math.random() - 0.5) * 10,
                            clientY: mouseY + (Math.random() - 0.5) * 10
                        });
                        document.dispatchEvent(event);
                    }
                }, 1000);
            });

            // BYPASS CAPTCHA DETECTION
            await page.evaluateOnNewDocument(() => {
                // Override canvas fingerprinting
                const getContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                    if (contextType === '2d') {
                        const context = getContext.call(this, contextType, contextAttributes);
                        const getImageData = context.getImageData;
                        context.getImageData = function(sx, sy, sw, sh) {
                            const imageData = getImageData.call(this, sx, sy, sw, sh);
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                imageData.data[i] += Math.floor(Math.random() * 10) - 5;
                                imageData.data[i + 1] += Math.floor(Math.random() * 10) - 5;
                                imageData.data[i + 2] += Math.floor(Math.random() * 10) - 5;
                            }
                            return imageData;
                        };
                        return context;
                    }
                    return getContext.call(this, contextType, contextAttributes);
                };

                // Override audio context fingerprinting
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                if (AudioContext) {
                    const getChannelData = AudioBuffer.prototype.getChannelData;
                    AudioBuffer.prototype.getChannelData = function(channel) {
                        const originalChannelData = getChannelData.call(this, channel);
                        for (let i = 0; i < originalChannelData.length; i++) {
                            originalChannelData[i] = originalChannelData[i] + Math.random() * 0.0001;
                        }
                        return originalChannelData;
                    };
                }
            });

            this.logger.info('✅ Anti-captcha features configured successfully');

        } catch (error) {
            this.logger.error('❌ Anti-captcha setup failed:', error);
        }
    }

    /**
     * Enable proxy after successful login (VN IP First Strategy)
     */
    async enableProxyAfterLogin(accountId, proxy) {
        try {
            this.logger.info('🔄 Enabling proxy after successful login...');

            const instance = this.instances.get(accountId);
            if (!instance || !instance.browser) {
                throw new Error('Browser instance not found');
            }

            const browser = instance.browser;
            const pages = await browser.pages();
            const page = pages[0] || await browser.newPage();

            // Setup proxy authentication
            if (proxy && proxy.ip && proxy.port) {
                const settings = await this.loadSettings();
                const proxyAuth = {
                    username: settings.proxyUsername || proxy.username,
                    password: settings.proxyPassword || proxy.password
                };

                this.logger.info(`🔐 Setting up proxy authentication: ${proxy.ip}:${proxy.port}`);

                // Setup proxy auth for the page
                await page.authenticate(proxyAuth);

                this.logger.info('✅ Proxy enabled successfully after login');

                // Verify proxy is working
                try {
                    await page.goto('https://httpbin.org/ip', { waitUntil: 'networkidle0', timeout: 10000 });
                    const ipInfo = await page.evaluate(() => {
                        try {
                            return JSON.parse(document.body.innerText);
                        } catch {
                            return { origin: 'unknown' };
                        }
                    });

                    this.logger.info(`🌐 Current IP after proxy enable: ${ipInfo.origin}`);

                    if (ipInfo.origin && ipInfo.origin.includes(proxy.ip)) {
                        this.logger.info('✅ Proxy verification successful');
                    } else {
                        this.logger.warn('⚠️ Proxy IP mismatch, but continuing...');
                    }

                    // Navigate back to Facebook
                    await page.goto('https://www.facebook.com/', { waitUntil: 'networkidle0', timeout: 15000 });
                    this.logger.info('🔄 Navigated back to Facebook with US proxy');
                } catch (verifyError) {
                    this.logger.warn('⚠️ Proxy verification failed, but continuing:', verifyError.message);
                }

                return true;
            }

            return false;

        } catch (error) {
            this.logger.error('❌ Failed to enable proxy after login:', error);
            return false;
        }
    }
}

module.exports = ChromeManager;
