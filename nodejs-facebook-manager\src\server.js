// FB NEXUS - Express Server Backend
const express = require('express');
const path = require('path');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const FacebookAutomation = require('./automation/FacebookAutomation');
const AccountManager = require('./managers/AccountManager');
const TaskManager = require('./managers/TaskManager');
const ChromeManager = require('./managers/ChromeManager');
const AutoLoginManager = require('./managers/AutoLoginManager');
const Logger = require('./utils/Logger');
const FacebookChecker = require('./facebook-checker');
const ProxyChecker = require('./proxy-checker');
const Database = require('./database');

class FBNexusServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.database = new Database();
        this.accountManager = new AccountManager();
        this.taskManager = new TaskManager();
        this.chromeManager = new ChromeManager();
        this.autoLoginManager = new AutoLoginManager();
        this.automation = new FacebookAutomation();
        this.logger = new Logger();
        this.facebookChecker = new FacebookChecker(this.logger);
        this.proxyChecker = new ProxyChecker(this.logger);
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // CORS
        this.app.use(cors());
        
        // JSON parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));
        
        // Static files
        this.app.use(express.static(path.join(__dirname, '../public')));
        
        // Logging middleware
        this.app.use((req, res, next) => {
            this.logger.info(`${req.method} ${req.path}`, { ip: req.ip });
            next();
        });
    }

    setupRoutes() {
        // Serve main page
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../public/index.html'));
        });

        // API Routes
        this.setupAccountRoutes();
        this.setupAutomationRoutes();
        this.setupTaskRoutes();
        this.setupStatsRoutes();
        this.setupLogsRoutes();
        this.setupProxyRoutes();
        this.setupSettingsRoutes();

        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({ success: false, message: 'Endpoint not found' });
        });

        // Error handler
        this.app.use((error, req, res, next) => {
            this.logger.error('Server error:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Internal server error',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        });
    }

    setupAccountRoutes() {
        // Get all accounts
        this.app.get('/api/accounts', async (req, res) => {
            try {
                const accounts = await this.accountManager.getAllAccounts();
                res.json({ success: true, accounts });
            } catch (error) {
                this.logger.error('Get accounts error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Add single account
        this.app.post('/api/accounts/add', async (req, res) => {
            try {
                const { email, password, twoFA, proxyId } = req.body;
                
                if (!email || !password) {
                    return res.status(400).json({ 
                        success: false, 
                        message: 'Email and password are required' 
                    });
                }

                const account = await this.accountManager.addAccount({
                    email,
                    password,
                    twoFA,
                    proxyId
                });

                this.logger.info('Account added:', { email });
                res.json({ success: true, account });

            } catch (error) {
                this.logger.error('Add account error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Bulk add accounts
        this.app.post('/api/accounts/bulk-add', async (req, res) => {
            try {
                const { accounts } = req.body;
                
                if (!Array.isArray(accounts) || accounts.length === 0) {
                    return res.status(400).json({ 
                        success: false, 
                        message: 'Accounts array is required' 
                    });
                }

                const results = await this.accountManager.bulkAddAccounts(accounts);
                
                this.logger.info('Bulk add accounts:', { count: results.length });
                res.json({ success: true, accounts: results });

            } catch (error) {
                this.logger.error('Bulk add accounts error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Launch browser for account
        this.app.post('/api/accounts/:id/launch-browser', async (req, res) => {
            try {
                const { id } = req.params;

                // Get account
                const account = await this.accountManager.getAccount(id);
                if (!account) {
                    return res.status(404).json({ success: false, message: 'Account not found' });
                }

                // Launch browser with fingerprint and proxy
                const result = await this.chromeManager.launchBrowser(account);

                this.logger.info('Browser launched for account:', { id, email: account.email });
                res.json({ success: true, message: 'Browser launched successfully', data: result });

            } catch (error) {
                this.logger.error('Launch browser error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Close browser for account
        this.app.post('/api/accounts/:id/close-browser', async (req, res) => {
            try {
                const { id } = req.params;

                // Close browser
                const result = await this.chromeManager.closeBrowser(id);

                this.logger.info('Browser closed for account:', { id });
                res.json({ success: true, message: 'Browser closed successfully', data: result });

            } catch (error) {
                this.logger.error('Close browser error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Arrange all browsers
        this.app.post('/api/browsers/arrange', async (req, res) => {
            try {
                this.logger.info('📐 Arranging all browsers...');

                // Get all active browser instances
                const result = await this.chromeManager.arrangeBrowserWindows();

                this.logger.info('✅ Browsers arranged successfully');
                res.json({
                    success: true,
                    message: 'Browsers arranged successfully',
                    data: result
                });

            } catch (error) {
                this.logger.error('❌ Arrange browsers error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Auto Login All Accounts
        this.app.post('/api/auto-login/start', async (req, res) => {
            try {
                this.logger.info('🚀 Starting auto login for all accounts...');
                const result = await this.autoLoginManager.startAutoLogin();
                res.json(result);
            } catch (error) {
                this.logger.error('❌ Auto login start error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        this.app.post('/api/auto-login/stop', async (req, res) => {
            try {
                const result = await this.autoLoginManager.stopAutoLogin();
                res.json(result);
            } catch (error) {
                this.logger.error('❌ Auto login stop error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        this.app.get('/api/auto-login/status', async (req, res) => {
            try {
                const status = this.autoLoginManager.getStatus();
                res.json({ success: true, data: status });
            } catch (error) {
                this.logger.error('❌ Auto login status error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        this.app.post('/api/auto-login/retry-captcha', async (req, res) => {
            try {
                const result = await this.autoLoginManager.retryCaptchaAccounts();
                res.json(result);
            } catch (error) {
                this.logger.error('❌ Auto login retry error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Login to Facebook
        this.app.post('/api/accounts/:id/login-facebook', async (req, res) => {
            try {
                const { id } = req.params;

                // Get account WITH credentials for login
                const account = await this.accountManager.getAccountWithCredentials(id);
                if (!account) {
                    return res.status(404).json({ success: false, message: 'Account not found' });
                }

                // Login to Facebook
                const result = await this.chromeManager.loginFacebook(account);

                // Update account status if login successful
                if (result.success) {
                    await this.accountManager.updateAccountStatus(id, {
                        status: 'logged_in',
                        lastLogin: new Date().toISOString(),
                        loginUrl: result.url || 'https://www.facebook.com',
                        isActive: true
                    });
                    this.logger.info(`✅ Account ${account.email} status updated to logged_in`);
                } else {
                    await this.accountManager.updateAccountStatus(id, {
                        status: 'login_failed',
                        lastLoginAttempt: new Date().toISOString(),
                        isActive: false
                    });
                    this.logger.warn(`❌ Account ${account.email} login failed`);
                }

                // Get updated account data
                const updatedAccount = await this.accountManager.getAccount(id);

                this.logger.info('Facebook login completed for account:', { id, email: account.email });
                res.json({
                    success: result.success,
                    message: result.success ? 'Facebook login successful' : 'Facebook login failed',
                    data: {
                        ...result,
                        account: updatedAccount
                    }
                });

            } catch (error) {
                this.logger.error('Facebook login error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Check account status (REAL CHECKER)
        this.app.post('/api/accounts/check/:id', async (req, res) => {
            try {
                const { id } = req.params;
                const account = await this.accountManager.getAccount(id);

                if (!account) {
                    return res.status(404).json({ success: false, message: 'Tài khoản không tồn tại' });
                }

                this.logger.info('🔍 Bắt đầu kiểm tra tài khoản:', { email: account.email });

                // Thực hiện check account thật với Puppeteer
                const checkResult = await this.facebookChecker.checkAccount(account);

                // Cập nhật account với kết quả check
                if (checkResult.success) {
                    await this.accountManager.updateAccount(id, {
                        status: checkResult.status,
                        lastChecked: checkResult.lastChecked,
                        name: checkResult.profileName || account.name,
                        notes: checkResult.details
                    });
                }

                res.json({
                    success: true,
                    result: checkResult,
                    message: checkResult.success ? 'Kiểm tra tài khoản thành công' : 'Kiểm tra tài khoản thất bại'
                });

            } catch (error) {
                this.logger.error('❌ Lỗi khi kiểm tra tài khoản:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Check all accounts (REAL CHECKER)
        this.app.post('/api/accounts/check-all', async (req, res) => {
            try {
                const accounts = await this.accountManager.getAllAccounts();

                if (accounts.length === 0) {
                    return res.json({ success: true, message: 'Không có tài khoản nào để kiểm tra', results: [] });
                }

                this.logger.info(`🚀 Bắt đầu kiểm tra hàng loạt ${accounts.length} tài khoản`);

                // Thực hiện check tất cả accounts với Puppeteer
                const results = await this.facebookChecker.checkMultipleAccounts(accounts, 2);

                // Cập nhật kết quả vào database
                for (const result of results) {
                    if (result.result.success) {
                        await this.accountManager.updateAccount(result.account.id, {
                            status: result.result.status,
                            lastChecked: result.result.lastChecked,
                            name: result.result.profileName || result.account.name,
                            notes: result.result.details
                        });
                    }
                }

                const successCount = results.filter(r => r.result.success).length;
                const failCount = results.length - successCount;

                this.logger.info(`✅ Hoàn thành kiểm tra: ${successCount} thành công, ${failCount} thất bại`);

                res.json({
                    success: true,
                    results: results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    },
                    message: `Đã kiểm tra ${results.length} tài khoản: ${successCount} thành công, ${failCount} thất bại`
                });

            } catch (error) {
                this.logger.error('❌ Lỗi khi kiểm tra hàng loạt:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Clear all accounts - MUST BE BEFORE /:id route
        this.app.delete('/api/accounts/clear-all', async (req, res) => {
            try {
                this.logger.info('🗑️ Starting to clear all accounts...');
                const result = await this.accountManager.clearAllAccounts();

                if (result) {
                    this.logger.info('✅ All accounts cleared successfully');
                    res.json({ success: true, message: 'All accounts cleared successfully' });
                } else {
                    this.logger.error('❌ Failed to clear accounts - database operation failed');
                    res.status(500).json({ success: false, message: 'Failed to clear accounts from database' });
                }

            } catch (error) {
                this.logger.error('❌ Clear all accounts error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Remove account
        this.app.delete('/api/accounts/:id', async (req, res) => {
            try {
                const { id } = req.params;
                await this.accountManager.removeAccount(id);

                this.logger.info('Account removed:', { id });
                res.json({ success: true, message: 'Account deleted successfully' });

            } catch (error) {
                this.logger.error('Remove account error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    setupAutomationRoutes() {
        // Start automation
        this.app.post('/api/automation/start', async (req, res) => {
            try {
                const { action, accounts, settings } = req.body;
                
                if (!action || !accounts || accounts.length === 0) {
                    return res.status(400).json({ 
                        success: false, 
                        message: 'Action and accounts are required' 
                    });
                }

                // Validate accounts exist
                const validAccounts = await this.accountManager.validateAccounts(accounts);
                if (validAccounts.length === 0) {
                    return res.status(400).json({ 
                        success: false, 
                        message: 'No valid accounts found' 
                    });
                }

                // Create automation task
                const taskId = uuidv4();
                const task = await this.taskManager.createTask({
                    id: taskId,
                    type: action,
                    accounts: validAccounts,
                    settings: settings || {},
                    status: 'starting'
                });

                // Start automation in background
                this.automation.startAutomation(task)
                    .then(() => {
                        this.logger.info('Automation started:', { taskId, action });
                    })
                    .catch(error => {
                        this.logger.error('Automation start error:', error);
                        this.taskManager.updateTaskStatus(taskId, 'failed', error.message);
                    });

                res.json({ 
                    success: true, 
                    taskId,
                    target: this.getDefaultTarget(action),
                    message: `${action} automation started successfully` 
                });

            } catch (error) {
                this.logger.error('Start automation error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Stop automation
        this.app.post('/api/automation/stop', async (req, res) => {
            try {
                const { taskId } = req.body;
                
                if (!taskId) {
                    return res.status(400).json({ 
                        success: false, 
                        message: 'Task ID is required' 
                    });
                }

                await this.automation.stopAutomation(taskId);
                await this.taskManager.updateTaskStatus(taskId, 'stopped');
                
                this.logger.info('Automation stopped:', { taskId });
                res.json({ success: true, message: 'Automation stopped successfully' });

            } catch (error) {
                this.logger.error('Stop automation error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Pause automation
        this.app.post('/api/automation/pause', async (req, res) => {
            try {
                const { taskId } = req.body;
                
                await this.automation.pauseAutomation(taskId);
                await this.taskManager.updateTaskStatus(taskId, 'paused');
                
                res.json({ success: true, message: 'Automation paused' });

            } catch (error) {
                this.logger.error('Pause automation error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Resume automation
        this.app.post('/api/automation/resume', async (req, res) => {
            try {
                const { taskId } = req.body;
                
                await this.automation.resumeAutomation(taskId);
                await this.taskManager.updateTaskStatus(taskId, 'running');
                
                res.json({ success: true, message: 'Automation resumed' });

            } catch (error) {
                this.logger.error('Resume automation error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    setupTaskRoutes() {
        // Get all tasks
        this.app.get('/api/tasks', async (req, res) => {
            try {
                const tasks = await this.taskManager.getAllTasks();
                res.json({ success: true, tasks });
            } catch (error) {
                this.logger.error('Get tasks error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Get task progress
        this.app.get('/api/tasks/progress', async (req, res) => {
            try {
                const tasks = await this.taskManager.getTasksProgress();
                res.json({ success: true, tasks });
            } catch (error) {
                this.logger.error('Get task progress error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Get task details
        this.app.get('/api/tasks/:id', async (req, res) => {
            try {
                const { id } = req.params;
                const task = await this.taskManager.getTask(id);
                
                if (!task) {
                    return res.status(404).json({ 
                        success: false, 
                        message: 'Task not found' 
                    });
                }

                res.json({ success: true, task });

            } catch (error) {
                this.logger.error('Get task error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    setupStatsRoutes() {
        // Get dashboard stats
        this.app.get('/api/stats', async (req, res) => {
            try {
                const accounts = await this.accountManager.getAllAccounts();
                const tasks = await this.taskManager.getAllTasks();
                
                const stats = {
                    totalAccounts: accounts.length,
                    activeAccounts: accounts.filter(acc => acc.status === 'active').length,
                    runningTasks: tasks.filter(task => task.status === 'running').length,
                    completedTasks: tasks.filter(task => task.status === 'completed').length,
                    successRate: tasks.length > 0 ? 
                        Math.round((tasks.filter(task => task.status === 'completed').length / tasks.length) * 100) : 0
                };

                res.json({ success: true, stats });

            } catch (error) {
                this.logger.error('Get stats error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    setupLogsRoutes() {
        // Get logs
        this.app.get('/api/logs', async (req, res) => {
            try {
                const { limit = 100, level = 'all' } = req.query;
                const logs = await this.logger.getLogs(parseInt(limit), level);

                res.json({ success: true, logs });

            } catch (error) {
                this.logger.error('Get logs error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Clear logs
        this.app.post('/api/logs/clear', async (req, res) => {
            try {
                await this.logger.clearLogs();
                res.json({ success: true, message: 'Logs cleared successfully' });
            } catch (error) {
                this.logger.error('Clear logs error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Export logs
        this.app.post('/api/logs/export', async (req, res) => {
            try {
                const logs = await this.logger.getLogs(1000, 'all');
                const exportData = {
                    exportedAt: new Date().toISOString(),
                    totalLogs: logs.length,
                    logs
                };

                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', `attachment; filename=fb-nexus-logs-${new Date().toISOString().split('T')[0]}.json`);
                res.json(exportData);

            } catch (error) {
                this.logger.error('Export logs error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    setupProxyRoutes() {
        // Get all proxies
        this.app.get('/api/proxies', async (req, res) => {
            try {
                const proxies = await this.database.getProxies();
                res.json({ success: true, proxies });
            } catch (error) {
                this.logger.error('Get proxies error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Get proxy statistics and classification
        this.app.get('/api/proxies/stats', async (req, res) => {
            try {
                const proxies = await this.database.getProxies();
                const accounts = await this.database.getAccounts();

                // Classify proxies
                const usProxies = proxies.filter(proxy => {
                    const country = (proxy.country || '').toLowerCase();
                    const location = (proxy.location || '').toLowerCase();
                    const notes = (proxy.notes || '').toLowerCase();
                    const ip = proxy.ip || '';

                    const isUSByLocation = country.includes('us') ||
                                         country.includes('usa') ||
                                         country.includes('united states') ||
                                         country.includes('america') ||
                                         location.includes('us') ||
                                         location.includes('usa') ||
                                         notes.includes('us') ||
                                         notes.includes('america');

                    const isUSByIP = ip.startsWith('192.') || ip.startsWith('10.') ||
                                   ip.startsWith('172.') || ip.startsWith('8.8.') ||
                                   ip.startsWith('1.1.') || ip.startsWith('4.') ||
                                   ip.startsWith('23.') || ip.startsWith('104.');

                    return isUSByLocation || isUSByIP;
                });

                const nonUSProxies = proxies.filter(proxy => !usProxies.includes(proxy));

                // Check usage
                const usedProxyIds = accounts.filter(acc => acc.proxyId).map(acc => acc.proxyId);
                const usedUSProxies = usProxies.filter(proxy => usedProxyIds.includes(proxy.id));
                const availableUSProxies = usProxies.filter(proxy => !usedProxyIds.includes(proxy.id));

                const stats = {
                    total: proxies.length,
                    us: {
                        total: usProxies.length,
                        available: availableUSProxies.length,
                        used: usedUSProxies.length,
                        proxies: usProxies.map(p => ({
                            id: p.id,
                            ip: p.ip,
                            port: p.port,
                            country: p.country,
                            isUsed: usedProxyIds.includes(p.id)
                        }))
                    },
                    nonUS: {
                        total: nonUSProxies.length,
                        proxies: nonUSProxies.map(p => ({
                            id: p.id,
                            ip: p.ip,
                            port: p.port,
                            country: p.country
                        }))
                    }
                };

                res.json({ success: true, stats });
            } catch (error) {
                this.logger.error('Get proxy stats error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Add proxy
        this.app.post('/api/proxies/add', async (req, res) => {
            try {
                const { ip, port, username, password, type } = req.body;

                const proxy = {
                    id: Date.now().toString(),
                    ip,
                    port: parseInt(port),
                    username: username || '',
                    password: password || '',
                    type: type || 'http',
                    status: 'untested',
                    country: 'Unknown',
                    speed: 0,
                    lastTested: null,
                    createdAt: new Date().toISOString()
                };

                const savedProxy = await this.database.addProxy(proxy);
                if (savedProxy) {
                    this.logger.info('✅ Proxy added to database:', { ip, port, type });
                    res.json({ success: true, proxy: savedProxy });
                } else {
                    throw new Error('Failed to save proxy to database');
                }

            } catch (error) {
                this.logger.error('❌ Add proxy error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Bulk add proxies
        this.app.post('/api/proxies/bulk-add', async (req, res) => {
            try {
                const { proxies } = req.body;

                if (!Array.isArray(proxies) || proxies.length === 0) {
                    return res.status(400).json({ success: false, message: 'Danh sách proxy là bắt buộc' });
                }

                const results = [];
                let addedCount = 0;

                for (const proxyData of proxies) {
                    try {
                        const proxy = {
                            id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
                            ip: proxyData.ip,
                            port: parseInt(proxyData.port),
                            username: proxyData.username || '',
                            password: proxyData.password || '',
                            type: proxyData.type || 'http',
                            status: 'untested',
                            country: 'Unknown',
                            speed: 0,
                            lastTested: null,
                            createdAt: new Date().toISOString()
                        };

                        const savedProxy = await this.database.addProxy(proxy);
                        if (savedProxy) {
                            results.push({ success: true, proxy: savedProxy });
                            addedCount++;
                        } else {
                            results.push({ success: false, error: 'Failed to save proxy' });
                        }
                    } catch (error) {
                        results.push({ success: false, error: error.message });
                    }
                }

                this.logger.info(`✅ Bulk added ${addedCount} proxies to database`);
                res.json({
                    success: true,
                    results,
                    summary: {
                        total: proxies.length,
                        added: addedCount,
                        failed: proxies.length - addedCount
                    },
                    message: `Đã thêm ${addedCount}/${proxies.length} proxy thành công`
                });

            } catch (error) {
                this.logger.error('❌ Bulk add proxies error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Clear all proxies (MUST BE BEFORE /:id route)
        this.app.delete('/api/proxies/clear-all', async (req, res) => {
            try {
                const result = await this.database.clearAllProxies();

                if (result) {
                    this.logger.info('All proxies cleared');
                    res.json({ success: true, message: 'All proxies cleared successfully' });
                } else {
                    res.status(500).json({ success: false, message: 'Failed to clear proxies' });
                }

            } catch (error) {
                this.logger.error('Clear all proxies error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Delete single proxy (MUST BE AFTER clear-all route)
        this.app.delete('/api/proxies/:id', async (req, res) => {
            try {
                const { id } = req.params;
                this.logger.info(`🗑️ Attempting to delete proxy with ID: ${id}`);

                // Get current proxies for debugging
                const currentProxies = await this.database.getProxies();
                this.logger.info(`📊 Current proxies count: ${currentProxies.length}`);

                // Log existing proxy IDs for debugging
                const existingIds = currentProxies.map(p => p.id);
                this.logger.info(`🔍 Existing proxy IDs: ${existingIds.join(', ')}`);

                // Remove proxy from database
                const result = await this.database.deleteProxy(id);

                if (result) {
                    this.logger.info('✅ Proxy removed successfully:', { id });
                    res.json({ success: true, message: 'Proxy deleted successfully' });
                } else {
                    this.logger.warn(`❌ Proxy not found for deletion: ${id}`);
                    res.status(404).json({ success: false, message: 'Proxy not found' });
                }

            } catch (error) {
                this.logger.error('❌ Delete proxy error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Test single proxy (REAL CHECKER)
        this.app.post('/api/proxies/test/:id', async (req, res) => {
            try {
                const { id } = req.params;

                // Tìm proxy trong danh sách (giả sử có storage)
                // Tạm thời tạo proxy object từ request body
                const { ip, port, username, password, type } = req.body;

                if (!ip || !port) {
                    return res.status(400).json({ success: false, message: 'IP và Port là bắt buộc' });
                }

                const proxy = {
                    id: id,
                    ip: ip,
                    port: parseInt(port),
                    username: username || '',
                    password: password || '',
                    type: type || 'http'
                };

                this.logger.info(`🔍 Bắt đầu test proxy: ${proxy.ip}:${proxy.port}`);

                // Thực hiện test proxy thật
                const testResult = await this.proxyChecker.checkProxy(proxy);

                res.json({
                    success: true,
                    result: testResult,
                    message: testResult.success ? 'Test proxy thành công' : 'Test proxy thất bại'
                });

            } catch (error) {
                this.logger.error('❌ Lỗi khi test proxy:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Test all proxies (REAL CHECKER)
        this.app.post('/api/proxies/test-all', async (req, res) => {
            try {
                const { proxies } = req.body;

                if (!Array.isArray(proxies) || proxies.length === 0) {
                    return res.status(400).json({ success: false, message: 'Danh sách proxy là bắt buộc' });
                }

                this.logger.info(`🚀 Bắt đầu test ${proxies.length} proxy`);

                // Thực hiện test tất cả proxies
                const results = await this.proxyChecker.checkMultipleProxies(proxies, 3);

                const workingCount = results.filter(r => r.result.success).length;
                const failedCount = results.length - workingCount;

                this.logger.info(`✅ Hoàn thành test proxy: ${workingCount} hoạt động, ${failedCount} lỗi`);

                res.json({
                    success: true,
                    results: results,
                    summary: {
                        total: results.length,
                        working: workingCount,
                        failed: failedCount
                    },
                    message: `Đã test ${results.length} proxy: ${workingCount} hoạt động, ${failedCount} lỗi`
                });

            } catch (error) {
                this.logger.error('❌ Lỗi khi test hàng loạt proxy:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Test proxy with Facebook (REAL CHECKER)
        this.app.post('/api/proxies/test-facebook/:id', async (req, res) => {
            try {
                const { id } = req.params;
                const { ip, port, username, password, type } = req.body;

                if (!ip || !port) {
                    return res.status(400).json({ success: false, message: 'IP và Port là bắt buộc' });
                }

                const proxy = {
                    id: id,
                    ip: ip,
                    port: parseInt(port),
                    username: username || '',
                    password: password || '',
                    type: type || 'http'
                };

                this.logger.info(`🔍 Test proxy với Facebook: ${proxy.ip}:${proxy.port}`);

                // Test proxy với Facebook thật
                const testResult = await this.proxyChecker.testProxyWithFacebook(proxy);

                res.json({
                    success: true,
                    result: testResult,
                    message: testResult.success ? 'Proxy có thể truy cập Facebook' : 'Proxy không thể truy cập Facebook'
                });

            } catch (error) {
                this.logger.error('❌ Lỗi khi test proxy với Facebook:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    setupSettingsRoutes() {
        // Get settings
        this.app.get('/api/settings', async (req, res) => {
            try {
                let settings = await this.database.getSettings();

                // Merge with default settings if empty
                if (Object.keys(settings).length === 0) {
                    settings = {
                        delayBetweenActions: 3000,
                        maxActionsSession: 50,
                        sessionBreak: 15,
                        randomizeDelays: true,
                        headlessMode: false,
                        disableImages: true,
                        maxBrowsers: 5,
                        browserTimeout: 30,
                        autoProxyRotation: true,
                        fingerprintSpoofing: true,
                        clearCookies: false,
                        userAgentRotation: 'random',
                        commentTemplates: [
                            'Bài viết hay quá! 👍',
                            'Cảm ơn bạn đã chia sẻ!',
                            'Thú vị quá! 🤔',
                            'Yêu thích! ❤️',
                            'Nội dung tuyệt vời!',
                            'Làm tốt lắm! 🔥'
                        ],
                        randomComments: true,
                        desktopNotifications: true,
                        soundNotifications: false,
                        emailNotifications: false,
                        notificationEmail: '',
                        logLevel: 'info',
                        autoBackup: true,
                        autoUpdateCheck: true,
                        apiRateLimit: 60
                    };
                    await this.database.saveSettings(settings);
                }

                res.json({ success: true, settings });

            } catch (error) {
                this.logger.error('❌ Get settings error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });

        // Save settings
        this.app.post('/api/settings', async (req, res) => {
            try {
                const settings = req.body;

                const saved = await this.database.saveSettings(settings);
                if (saved) {
                    this.logger.info('✅ Settings saved to database:', Object.keys(settings));
                    res.json({ success: true, message: 'Đã lưu cài đặt thành công' });
                } else {
                    throw new Error('Failed to save settings to database');
                }

            } catch (error) {
                this.logger.error('❌ Save settings error:', error);
                res.status(500).json({ success: false, message: error.message });
            }
        });
    }

    getDefaultTarget(action) {
        const targets = {
            like: 'Auto-detected posts',
            comment: 'Auto-detected posts',
            follow: 'Suggested users',
            share: 'Auto-detected posts'
        };
        return targets[action] || 'Auto-detected';
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`🚀 FB NEXUS Server running on http://localhost:${this.port}`);
            console.log(`📊 Dashboard: http://localhost:${this.port}`);
            this.logger.info('Server started', { port: this.port });
        });
    }
}

module.exports = FBNexusServer;
